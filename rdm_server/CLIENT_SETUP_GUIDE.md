# TaskFlow 客户端配置指南

## 🎯 问题解决状态

✅ **问题已修复！** demo_user 现在可以成功登录并访问数据。

## 📋 服务器配置信息

### Parse Server 端点
- **服务器地址**: `http://************:3000/parse`
- **本地地址**: `http://localhost:3000/parse`
- **应用ID**: `taskflow-app-id`
- **REST API Key**: `taskflow-rest-key`
- **JavaScript Key**: `taskflow-js-key`

### 测试用户凭据
- **用户名**: `demo_user`
- **邮箱**: `<EMAIL>`
- **密码**: `Test123!`
- **用户ID**: `yie8jNo4JN`
- **权限**: 普通用户

## 🔧 iOS客户端配置

### 1. Parse SDK 初始化

```swift
import Parse

// 在 AppDelegate 或 App.swift 中配置
func configureParse() {
    let configuration = ParseClientConfiguration {
        $0.applicationId = "taskflow-app-id"
        $0.clientKey = "taskflow-js-key"
        $0.server = "http://************:3000/parse"
    }
    Parse.initialize(with: configuration)
}
```

### 2. 用户登录示例

```swift
import Parse

func loginUser() {
    PFUser.logInWithUsername(inBackground: "demo_user", password: "Test123!") { (user, error) in
        if let user = user {
            print("登录成功: \(user.username ?? "")")
            print("用户ID: \(user.objectId ?? "")")
            print("邮箱: \(user.email ?? "")")
        } else if let error = error {
            print("登录失败: \(error.localizedDescription)")
        }
    }
}
```

### 3. 获取任务数据

```swift
import Parse

func fetchTasks() {
    let query = PFQuery(className: "Task")
    query.whereKey("userId", equalTo: PFUser.current()?.objectId ?? "")
    query.includeKey("user")
    
    query.findObjectsInBackground { (tasks, error) in
        if let tasks = tasks {
            print("获取到 \(tasks.count) 个任务")
            for task in tasks {
                print("任务: \(task["title"] ?? "")")
                print("状态: \(task["status"] ?? "")")
                print("优先级: \(task["priority"] ?? "")")
            }
        } else if let error = error {
            print("获取任务失败: \(error.localizedDescription)")
        }
    }
}
```

### 4. 获取习惯数据

```swift
import Parse

func fetchHabits() {
    let query = PFQuery(className: "Habit")
    query.whereKey("userId", equalTo: PFUser.current()?.objectId ?? "")
    query.includeKey("user")
    
    query.findObjectsInBackground { (habits, error) in
        if let habits = habits {
            print("获取到 \(habits.count) 个习惯")
            for habit in habits {
                print("习惯: \(habit["name"] ?? "")")
                print("类型: \(habit["type"] ?? "")")
                print("频率: \(habit["frequency"] ?? "")")
            }
        } else if let error = error {
            print("获取习惯失败: \(error.localizedDescription)")
        }
    }
}
```

## 🧪 测试验证

### 1. 服务器状态检查

```bash
# 检查服务器健康状态
curl http://************:3000/health

# 预期响应
{
  "status": "ok",
  "timestamp": "2025-08-25T09:26:50.262Z",
  "mongodb": "connected",
  "parseServer": "running"
}
```

### 2. 用户登录测试

```bash
# 测试用户登录
curl -X POST http://************:3000/parse/login \
  -H "X-Parse-Application-Id: taskflow-app-id" \
  -H "X-Parse-REST-API-Key: taskflow-rest-key" \
  -H "Content-Type: application/json" \
  -d '{"username":"demo_user","password":"Test123!"}'

# 预期响应（包含sessionToken）
{
  "objectId": "yie8jNo4JN",
  "username": "demo_user",
  "email": "<EMAIL>",
  "sessionToken": "r:9f2a55e627584dbbad85d00733730e9c",
  ...
}
```

### 3. 数据访问测试

```bash
# 获取任务数据（需要替换sessionToken）
curl -X GET "http://************:3000/parse/classes/Task" \
  -H "X-Parse-Application-Id: taskflow-app-id" \
  -H "X-Parse-REST-API-Key: taskflow-rest-key" \
  -H "X-Parse-Session-Token: YOUR_SESSION_TOKEN"

# 获取习惯数据
curl -X GET "http://************:3000/parse/classes/Habit" \
  -H "X-Parse-Application-Id: taskflow-app-id" \
  -H "X-Parse-REST-API-Key: taskflow-rest-key" \
  -H "X-Parse-Session-Token: YOUR_SESSION_TOKEN"
```

## 📊 可用测试数据

### 任务数据示例
- 20个任务记录
- 包含不同优先级：high, medium, low
- 包含不同状态：pending, in_progress, completed
- 包含不同分类：工作, 学习, 健康, 生活

### 习惯数据示例
- 7个习惯记录
- 包含不同类型：duration, boolean, numeric
- 包含不同频率：daily
- 包含目标值和单位

## 🔒 安全配置

### ACL权限
- 每个用户只能访问自己的数据
- 自动设置读写权限：`{"userId": {"read": true, "write": true}}`

### 会话管理
- 会话令牌有效期：7天
- 密码重置时撤销会话
- 支持会话令牌验证

## 🚨 故障排除

### 常见问题

1. **"unauthorized" 错误**
   - 检查 Application ID 和 REST API Key
   - 确保使用正确的服务器地址
   - 验证用户凭据

2. **网络连接问题**
   - 确保服务器正在运行：`http://************:3000/health`
   - 检查防火墙设置
   - 验证网络连接

3. **数据访问问题**
   - 确保用户已登录并获得有效的 sessionToken
   - 检查 ACL 权限设置
   - 验证查询条件

### 调试步骤

1. 检查服务器状态
2. 测试用户登录
3. 验证 sessionToken
4. 测试数据查询
5. 检查网络日志

## 📞 支持信息

- **服务器地址**: http://************:3000
- **管理后台**: http://************:3000/admin (admin/admin123)
- **健康检查**: http://************:3000/health

---

**状态**: ✅ 已修复并测试通过
**最后更新**: 2025-08-25
**测试用户**: demo_user 可正常登录和访问数据

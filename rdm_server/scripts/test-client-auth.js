#!/usr/bin/env node

/**
 * TaskFlow 客户端认证测试脚本
 * 验证客户端登录和数据访问功能
 */

const axios = require('axios');

// 配置
const CONFIG = {
  serverURL: 'http://10.0.136.252:3000',
  parseURL: 'http://10.0.136.252:3000/parse',
  appId: 'taskflow-app-id',
  restKey: 'taskflow-rest-key',
  testUser: {
    username: 'demo_user',
    password: 'Test123!',
    email: '<EMAIL>'
  }
};

// 测试结果
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  tests: []
};

// 工具函数
function logTest(name, passed, message = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}: ${message}`);
  }
  testResults.tests.push({ name, passed, message });
}

function logInfo(message) {
  console.log(`ℹ️  ${message}`);
}

function logSection(title) {
  console.log(`\n🔍 ${title}`);
  console.log('='.repeat(50));
}

// HTTP请求工具
async function makeRequest(method, url, headers = {}, data = null) {
  try {
    const config = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message,
      status: error.response?.status || 0
    };
  }
}

// 测试函数
async function testServerHealth() {
  logSection('服务器健康检查');
  
  const result = await makeRequest('GET', `${CONFIG.serverURL}/health`);
  
  if (result.success) {
    logTest('服务器响应正常', true);
    logTest('MongoDB连接状态', result.data.mongodb === 'connected');
    logTest('Parse Server状态', result.data.parseServer === 'running');
    logInfo(`服务器时间: ${result.data.timestamp}`);
  } else {
    logTest('服务器健康检查', false, result.error);
  }
  
  return result.success;
}

async function testUserLogin() {
  logSection('用户登录测试');
  
  const headers = {
    'X-Parse-Application-Id': CONFIG.appId,
    'X-Parse-REST-API-Key': CONFIG.restKey
  };
  
  const loginData = {
    username: CONFIG.testUser.username,
    password: CONFIG.testUser.password
  };
  
  const result = await makeRequest('POST', `${CONFIG.parseURL}/login`, headers, loginData);
  
  if (result.success) {
    logTest('用户登录成功', true);
    logTest('返回用户信息', !!result.data.objectId);
    logTest('返回会话令牌', !!result.data.sessionToken);
    logTest('用户名匹配', result.data.username === CONFIG.testUser.username);
    logTest('邮箱匹配', result.data.email === CONFIG.testUser.email);
    
    logInfo(`用户ID: ${result.data.objectId}`);
    logInfo(`会话令牌: ${result.data.sessionToken.substring(0, 20)}...`);
    
    return result.data;
  } else {
    logTest('用户登录', false, result.error);
    return null;
  }
}

async function testDataAccess(sessionToken) {
  logSection('数据访问测试');
  
  const headers = {
    'X-Parse-Application-Id': CONFIG.appId,
    'X-Parse-REST-API-Key': CONFIG.restKey,
    'X-Parse-Session-Token': sessionToken
  };
  
  // 测试任务数据访问
  const taskResult = await makeRequest('GET', `${CONFIG.parseURL}/classes/Task`, headers);
  
  if (taskResult.success) {
    logTest('任务数据访问', true);
    logTest('任务数据格式正确', Array.isArray(taskResult.data.results));
    logTest('任务数据非空', taskResult.data.results.length > 0);
    
    if (taskResult.data.results.length > 0) {
      const firstTask = taskResult.data.results[0];
      logTest('任务包含必要字段', !!(firstTask.title && firstTask.status && firstTask.priority));
      logInfo(`获取到 ${taskResult.data.results.length} 个任务`);
    }
  } else {
    logTest('任务数据访问', false, taskResult.error);
  }
  
  // 测试习惯数据访问
  const habitResult = await makeRequest('GET', `${CONFIG.parseURL}/classes/Habit`, headers);
  
  if (habitResult.success) {
    logTest('习惯数据访问', true);
    logTest('习惯数据格式正确', Array.isArray(habitResult.data.results));
    logTest('习惯数据非空', habitResult.data.results.length > 0);
    
    if (habitResult.data.results.length > 0) {
      const firstHabit = habitResult.data.results[0];
      logTest('习惯包含必要字段', !!(firstHabit.name && firstHabit.type && firstHabit.frequency));
      logInfo(`获取到 ${habitResult.data.results.length} 个习惯`);
    }
  } else {
    logTest('习惯数据访问', false, habitResult.error);
  }
  
  return taskResult.success && habitResult.success;
}

async function testDataCreation(sessionToken) {
  logSection('数据创建测试');
  
  const headers = {
    'X-Parse-Application-Id': CONFIG.appId,
    'X-Parse-REST-API-Key': CONFIG.restKey,
    'X-Parse-Session-Token': sessionToken
  };
  
  // 创建测试任务
  const newTask = {
    title: '测试任务',
    description: '这是一个测试任务',
    priority: 'medium',
    status: 'pending',
    category: '测试',
    isCompleted: false,
    tags: ['测试']
  };
  
  const createResult = await makeRequest('POST', `${CONFIG.parseURL}/classes/Task`, headers, newTask);
  
  if (createResult.success) {
    logTest('创建任务成功', true);
    logTest('返回对象ID', !!createResult.data.objectId);
    logInfo(`创建的任务ID: ${createResult.data.objectId}`);
    
    // 删除测试任务
    const deleteResult = await makeRequest('DELETE', `${CONFIG.parseURL}/classes/Task/${createResult.data.objectId}`, headers);
    logTest('删除测试任务', deleteResult.success);
    
    return true;
  } else {
    logTest('创建任务', false, createResult.error);
    return false;
  }
}

async function testInvalidCredentials() {
  logSection('无效凭据测试');
  
  const headers = {
    'X-Parse-Application-Id': CONFIG.appId,
    'X-Parse-REST-API-Key': CONFIG.restKey
  };
  
  const invalidLogin = {
    username: 'invalid_user',
    password: 'wrong_password'
  };
  
  const result = await makeRequest('POST', `${CONFIG.parseURL}/login`, headers, invalidLogin);
  
  logTest('无效凭据被拒绝', !result.success);
  
  return !result.success;
}

// 主测试函数
async function runTests() {
  console.log('🚀 TaskFlow 客户端认证测试');
  console.log('='.repeat(50));
  console.log(`测试服务器: ${CONFIG.serverURL}`);
  console.log(`测试用户: ${CONFIG.testUser.username}`);
  console.log('');
  
  try {
    // 1. 服务器健康检查
    const serverHealthy = await testServerHealth();
    if (!serverHealthy) {
      console.log('\n❌ 服务器不可用，停止测试');
      return;
    }
    
    // 2. 用户登录测试
    const userInfo = await testUserLogin();
    if (!userInfo) {
      console.log('\n❌ 用户登录失败，停止测试');
      return;
    }
    
    // 3. 数据访问测试
    await testDataAccess(userInfo.sessionToken);
    
    // 4. 数据创建测试
    await testDataCreation(userInfo.sessionToken);
    
    // 5. 无效凭据测试
    await testInvalidCredentials();
    
  } catch (error) {
    console.error('\n💥 测试过程中发生错误:', error.message);
  }
  
  // 输出测试结果
  logSection('测试结果汇总');
  console.log(`总测试数: ${testResults.total}`);
  console.log(`通过: ${testResults.passed}`);
  console.log(`失败: ${testResults.failed}`);
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.tests
      .filter(test => !test.passed)
      .forEach(test => console.log(`  - ${test.name}: ${test.message}`));
  }
  
  console.log('\n' + (testResults.failed === 0 ? '🎉 所有测试通过！' : '⚠️  部分测试失败'));
  
  // 输出客户端配置信息
  if (testResults.failed === 0) {
    console.log('\n📱 iOS客户端配置信息:');
    console.log(`服务器地址: ${CONFIG.parseURL}`);
    console.log(`应用ID: ${CONFIG.appId}`);
    console.log(`REST API Key: ${CONFIG.restKey}`);
    console.log(`测试用户: ${CONFIG.testUser.username} / ${CONFIG.testUser.password}`);
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, CONFIG };

#!/usr/bin/env node

/**
 * 测试所有可能的Parse API端点
 * 帮助客户端开发者找到正确的配置
 */

const axios = require('axios');

const CONFIG = {
  baseURL: 'http://10.0.136.252:3000',
  appId: 'taskflow-app-id',
  restKey: 'taskflow-rest-key',
  jsKey: 'taskflow-js-key',
  testUser: {
    username: 'demo_user',
    password: 'Test123!'
  }
};

// 测试不同的端点配置
const ENDPOINTS_TO_TEST = [
  // 标准Parse端点
  { path: '/parse/login', name: '标准Parse登录端点' },
  { path: '/parse/1/login', name: 'Parse v1登录端点' },
  { path: '/parse/users', name: 'Parse用户端点 (POST)', method: 'POST' },
  { path: '/1/login', name: '简化v1登录端点' },
  { path: '/login', name: '根登录端点' },
  
  // 健康检查
  { path: '/health', name: '健康检查端点', method: 'GET', noAuth: true },
  { path: '/parse', name: 'Parse根端点', method: 'GET' },
];

async function testEndpoint(endpoint) {
  const url = `${CONFIG.baseURL}${endpoint.path}`;
  const method = endpoint.method || 'POST';
  
  console.log(`\n🔍 测试: ${endpoint.name}`);
  console.log(`   URL: ${method} ${url}`);
  
  try {
    const headers = {
      'Content-Type': 'application/json'
    };
    
    // 添加认证头 (除非明确不需要)
    if (!endpoint.noAuth) {
      headers['X-Parse-Application-Id'] = CONFIG.appId;
      headers['X-Parse-REST-API-Key'] = CONFIG.restKey;
    }
    
    const config = {
      method,
      url,
      headers,
      timeout: 5000
    };
    
    // 对于POST请求，添加登录数据
    if (method === 'POST' && !endpoint.noAuth) {
      config.data = CONFIG.testUser;
    }
    
    const response = await axios(config);
    
    console.log(`   ✅ 状态: ${response.status}`);
    
    if (response.data) {
      if (response.data.sessionToken) {
        console.log(`   🎉 登录成功! SessionToken: ${response.data.sessionToken.substring(0, 20)}...`);
        return { success: true, endpoint, sessionToken: response.data.sessionToken };
      } else if (response.data.status === 'ok') {
        console.log(`   ✅ 健康检查通过`);
        return { success: true, endpoint };
      } else {
        console.log(`   📄 响应: ${JSON.stringify(response.data).substring(0, 100)}...`);
        return { success: true, endpoint };
      }
    }
    
  } catch (error) {
    if (error.response) {
      console.log(`   ❌ HTTP ${error.response.status}: ${error.response.statusText}`);
      if (error.response.status === 401) {
        console.log(`   💡 提示: 端点存在但需要正确的认证`);
      } else if (error.response.status === 404) {
        console.log(`   💡 提示: 端点不存在`);
      }
      
      if (error.response.data) {
        const errorData = typeof error.response.data === 'string' 
          ? error.response.data.substring(0, 100)
          : JSON.stringify(error.response.data).substring(0, 100);
        console.log(`   📄 错误详情: ${errorData}...`);
      }
    } else if (error.code === 'ECONNREFUSED') {
      console.log(`   ❌ 连接被拒绝: 服务器可能未运行`);
    } else if (error.code === 'ETIMEDOUT') {
      console.log(`   ❌ 请求超时`);
    } else {
      console.log(`   ❌ 网络错误: ${error.message}`);
    }
    
    return { success: false, endpoint, error };
  }
}

async function testDataAccess(sessionToken) {
  console.log(`\n📊 测试数据访问 (使用sessionToken)`);
  
  const endpoints = [
    '/parse/classes/Task',
    '/parse/classes/Habit',
    '/parse/classes/User'
  ];
  
  for (const endpoint of endpoints) {
    const url = `${CONFIG.baseURL}${endpoint}`;
    console.log(`\n   测试: GET ${url}`);
    
    try {
      const response = await axios.get(url, {
        headers: {
          'X-Parse-Application-Id': CONFIG.appId,
          'X-Parse-REST-API-Key': CONFIG.restKey,
          'X-Parse-Session-Token': sessionToken
        },
        timeout: 5000
      });
      
      console.log(`   ✅ 状态: ${response.status}`);
      if (response.data && response.data.results) {
        console.log(`   📊 获取到 ${response.data.results.length} 条记录`);
      }
      
    } catch (error) {
      if (error.response) {
        console.log(`   ❌ HTTP ${error.response.status}: ${error.response.statusText}`);
      } else {
        console.log(`   ❌ 错误: ${error.message}`);
      }
    }
  }
}

async function generateClientConfig(workingEndpoint) {
  console.log(`\n📱 推荐的客户端配置:`);
  console.log(`=`.repeat(50));
  
  if (workingEndpoint) {
    const serverURL = `${CONFIG.baseURL}/parse`;
    
    console.log(`\n🔧 iOS Parse SDK 配置:`);
    console.log(`
let configuration = ParseClientConfiguration {
    $0.applicationId = "${CONFIG.appId}"
    $0.clientKey = "${CONFIG.jsKey}"
    $0.server = "${serverURL}"
}
Parse.initialize(with: configuration)
`);
    
    console.log(`\n🔧 JavaScript/React Native 配置:`);
    console.log(`
Parse.initialize("${CONFIG.appId}", "${CONFIG.jsKey}");
Parse.serverURL = "${serverURL}";
`);
    
    console.log(`\n🔧 直接HTTP请求配置:`);
    console.log(`
Base URL: ${serverURL}
Headers:
  X-Parse-Application-Id: ${CONFIG.appId}
  X-Parse-REST-API-Key: ${CONFIG.restKey}
  Content-Type: application/json
`);
    
  } else {
    console.log(`\n❌ 没有找到可用的登录端点!`);
    console.log(`请检查服务器配置和网络连接。`);
  }
}

async function main() {
  console.log(`🚀 TaskFlow Parse API 端点测试`);
  console.log(`=`.repeat(50));
  console.log(`服务器: ${CONFIG.baseURL}`);
  console.log(`测试用户: ${CONFIG.testUser.username}`);
  
  let workingEndpoint = null;
  let sessionToken = null;
  
  // 测试所有端点
  for (const endpoint of ENDPOINTS_TO_TEST) {
    const result = await testEndpoint(endpoint);
    
    if (result.success && result.sessionToken) {
      workingEndpoint = result.endpoint;
      sessionToken = result.sessionToken;
    }
  }
  
  // 如果找到可用的登录端点，测试数据访问
  if (sessionToken) {
    await testDataAccess(sessionToken);
  }
  
  // 生成客户端配置建议
  await generateClientConfig(workingEndpoint);
  
  // 总结
  console.log(`\n📋 测试总结:`);
  console.log(`=`.repeat(50));
  
  if (workingEndpoint) {
    console.log(`✅ 找到可用的登录端点: ${workingEndpoint.name}`);
    console.log(`✅ 用户认证成功`);
    if (sessionToken) {
      console.log(`✅ 会话令牌获取成功`);
    }
    console.log(`\n🎯 建议客户端使用以下配置:`);
    console.log(`   服务器地址: ${CONFIG.baseURL}/parse`);
    console.log(`   应用ID: ${CONFIG.appId}`);
    console.log(`   客户端密钥: ${CONFIG.jsKey}`);
  } else {
    console.log(`❌ 没有找到可用的登录端点`);
    console.log(`❌ 请检查服务器配置`);
  }
  
  console.log(`\n💡 如果客户端仍然遇到问题，请检查:`);
  console.log(`   1. 网络连接是否正常`);
  console.log(`   2. 服务器地址是否正确`);
  console.log(`   3. Parse SDK版本是否兼容`);
  console.log(`   4. 认证密钥是否正确`);
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, CONFIG };

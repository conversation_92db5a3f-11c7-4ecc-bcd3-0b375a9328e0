#!/usr/bin/env node

/**
 * 模拟iOS客户端请求
 * 帮助诊断客户端"资源未找到"问题
 */

const axios = require('axios');

const CONFIG = {
  baseURL: 'http://************:3000',
  parseURL: 'http://************:3000/parse',
  appId: 'taskflow-app-id',
  clientKey: 'taskflow-js-key',
  restKey: 'taskflow-rest-key',
  testUser: {
    username: 'demo_user',
    password: 'Test123!'
  }
};

// 模拟不同的客户端配置
const CLIENT_CONFIGS = [
  {
    name: 'iOS Parse SDK (推荐配置)',
    headers: {
      'X-Parse-Application-Id': CONFIG.appId,
      'X-Parse-Client-Key': CONFIG.clientKey,
      'Content-Type': 'application/json',
      'User-Agent': 'TaskFlow-iOS/1.0 (iPhone; iOS 17.0)'
    },
    endpoint: '/parse/login'
  },
  {
    name: 'iOS Parse SDK (REST API Key)',
    headers: {
      'X-Parse-Application-Id': CONFIG.appId,
      'X-Parse-REST-API-Key': CONFIG.restKey,
      'Content-Type': 'application/json',
      'User-Agent': 'TaskFlow-iOS/1.0 (iPhone; iOS 17.0)'
    },
    endpoint: '/parse/login'
  },
  {
    name: 'iOS Parse SDK (版本化端点)',
    headers: {
      'X-Parse-Application-Id': CONFIG.appId,
      'X-Parse-Client-Key': CONFIG.clientKey,
      'Content-Type': 'application/json',
      'User-Agent': 'TaskFlow-iOS/1.0 (iPhone; iOS 17.0)'
    },
    endpoint: '/parse/1/login'
  },
  {
    name: 'iOS Parse SDK (错误的服务器地址)',
    headers: {
      'X-Parse-Application-Id': CONFIG.appId,
      'X-Parse-Client-Key': CONFIG.clientKey,
      'Content-Type': 'application/json',
      'User-Agent': 'TaskFlow-iOS/1.0 (iPhone; iOS 17.0)'
    },
    endpoint: '/login',
    baseURL: 'http://************:3000'
  },
  {
    name: 'iOS Parse SDK (缺少Application ID)',
    headers: {
      'X-Parse-Client-Key': CONFIG.clientKey,
      'Content-Type': 'application/json',
      'User-Agent': 'TaskFlow-iOS/1.0 (iPhone; iOS 17.0)'
    },
    endpoint: '/parse/login'
  }
];

async function testClientConfig(config) {
  console.log(`\n🧪 测试配置: ${config.name}`);
  console.log('='.repeat(60));
  
  const url = (config.baseURL || CONFIG.baseURL) + config.endpoint;
  console.log(`URL: POST ${url}`);
  console.log(`Headers:`, config.headers);
  
  try {
    const response = await axios.post(url, CONFIG.testUser, {
      headers: config.headers,
      timeout: 10000
    });
    
    console.log(`✅ 状态: ${response.status}`);
    if (response.data.sessionToken) {
      console.log(`🎉 登录成功!`);
      console.log(`   用户ID: ${response.data.objectId}`);
      console.log(`   用户名: ${response.data.username}`);
      console.log(`   会话令牌: ${response.data.sessionToken.substring(0, 20)}...`);
      return { success: true, config, sessionToken: response.data.sessionToken };
    } else {
      console.log(`📄 响应:`, response.data);
      return { success: true, config };
    }
    
  } catch (error) {
    if (error.response) {
      console.log(`❌ HTTP ${error.response.status}: ${error.response.statusText}`);
      
      // 详细错误分析
      switch (error.response.status) {
        case 404:
          console.log(`💡 诊断: 资源未找到 - 端点不存在或服务器地址错误`);
          console.log(`   检查项:`);
          console.log(`   - 服务器地址是否正确`);
          console.log(`   - 端点路径是否正确`);
          console.log(`   - Parse Server是否正在运行`);
          break;
        case 401:
          console.log(`💡 诊断: 未授权 - 认证信息错误`);
          console.log(`   检查项:`);
          console.log(`   - Application ID是否正确`);
          console.log(`   - Client Key是否正确`);
          break;
        case 400:
          console.log(`💡 诊断: 请求错误 - 请求格式或数据错误`);
          break;
        default:
          console.log(`💡 诊断: 其他HTTP错误`);
      }
      
      if (error.response.data) {
        const errorData = typeof error.response.data === 'string' 
          ? error.response.data.substring(0, 200)
          : JSON.stringify(error.response.data, null, 2);
        console.log(`📄 错误详情:\n${errorData}`);
      }
    } else if (error.code === 'ECONNREFUSED') {
      console.log(`❌ 连接被拒绝`);
      console.log(`💡 诊断: 服务器未运行或网络不可达`);
    } else if (error.code === 'ETIMEDOUT') {
      console.log(`❌ 请求超时`);
      console.log(`💡 诊断: 网络连接问题或服务器响应慢`);
    } else {
      console.log(`❌ 网络错误: ${error.message}`);
    }
    
    return { success: false, config, error };
  }
}

async function testDebugEndpoint() {
  console.log(`\n🔍 测试调试端点`);
  console.log('='.repeat(60));
  
  try {
    const response = await axios.post(`${CONFIG.baseURL}/debug/client-test`, CONFIG.testUser, {
      headers: {
        'X-Parse-Application-Id': CONFIG.appId,
        'X-Parse-Client-Key': CONFIG.clientKey,
        'Content-Type': 'application/json',
        'User-Agent': 'TaskFlow-iOS/1.0 (iPhone; iOS 17.0)'
      }
    });
    
    console.log(`✅ 调试端点响应:`);
    console.log(JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log(`❌ 调试端点测试失败:`, error.message);
  }
}

async function testDataAccess(sessionToken) {
  console.log(`\n📊 测试数据访问`);
  console.log('='.repeat(60));
  
  const endpoints = [
    { path: '/parse/classes/Task', name: '任务数据' },
    { path: '/parse/classes/Habit', name: '习惯数据' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(`${CONFIG.baseURL}${endpoint.path}`, {
        headers: {
          'X-Parse-Application-Id': CONFIG.appId,
          'X-Parse-Client-Key': CONFIG.clientKey,
          'X-Parse-Session-Token': sessionToken,
          'User-Agent': 'TaskFlow-iOS/1.0 (iPhone; iOS 17.0)'
        }
      });
      
      console.log(`✅ ${endpoint.name}: ${response.data.results.length} 条记录`);
      
    } catch (error) {
      console.log(`❌ ${endpoint.name}: ${error.response?.status || error.message}`);
    }
  }
}

async function generateIOSCode(workingConfig) {
  console.log(`\n📱 生成iOS代码`);
  console.log('='.repeat(60));
  
  if (workingConfig) {
    console.log(`基于配置: ${workingConfig.name}`);
    
    const serverURL = workingConfig.baseURL || CONFIG.baseURL;
    const parseURL = serverURL.includes('/parse') ? serverURL : `${serverURL}/parse`;
    
    console.log(`
// AppDelegate.swift 或 App.swift 中的配置
import Parse

func configureParse() {
    let configuration = ParseClientConfiguration {
        $0.applicationId = "${CONFIG.appId}"
        $0.clientKey = "${CONFIG.clientKey}"
        $0.server = "${parseURL}"
    }
    Parse.initialize(with: configuration)
    
    // 可选：启用调试日志
    Parse.setLogLevel(.debug)
}

// 登录测试代码
func testLogin() {
    PFUser.logInWithUsername(inBackground: "${CONFIG.testUser.username}", 
                           password: "${CONFIG.testUser.password}") { (user, error) in
        if let user = user {
            print("✅ 登录成功: \\(user.username ?? "")")
            print("用户ID: \\(user.objectId ?? "")")
        } else if let error = error {
            print("❌ 登录失败: \\(error.localizedDescription)")
            print("错误代码: \\((error as NSError).code)")
            
            // 详细错误处理
            switch (error as NSError).code {
            case 100: print("网络连接问题")
            case 101: print("用户名或密码错误")
            case 124: print("请求超时")
            default: print("其他错误")
            }
        }
    }
}
`);
  } else {
    console.log(`❌ 没有找到可用的配置`);
  }
}

async function main() {
  console.log(`🚀 TaskFlow iOS 客户端模拟测试`);
  console.log(`=`.repeat(60));
  console.log(`服务器: ${CONFIG.baseURL}`);
  console.log(`Parse端点: ${CONFIG.parseURL}`);
  console.log(`测试用户: ${CONFIG.testUser.username}`);
  
  // 测试调试端点
  await testDebugEndpoint();
  
  let workingConfig = null;
  let sessionToken = null;
  
  // 测试所有客户端配置
  for (const config of CLIENT_CONFIGS) {
    const result = await testClientConfig(config);
    
    if (result.success && result.sessionToken) {
      workingConfig = result.config;
      sessionToken = result.sessionToken;
      break; // 找到可用配置就停止
    }
  }
  
  // 如果找到可用配置，测试数据访问
  if (sessionToken) {
    await testDataAccess(sessionToken);
  }
  
  // 生成iOS代码
  await generateIOSCode(workingConfig);
  
  // 总结
  console.log(`\n📋 测试总结`);
  console.log('='.repeat(60));
  
  if (workingConfig) {
    console.log(`✅ 找到可用配置: ${workingConfig.name}`);
    console.log(`✅ 用户认证成功`);
    if (sessionToken) {
      console.log(`✅ 数据访问正常`);
    }
    
    console.log(`\n🎯 推荐的iOS配置:`);
    console.log(`   服务器地址: ${CONFIG.parseURL}`);
    console.log(`   应用ID: ${CONFIG.appId}`);
    console.log(`   客户端密钥: ${CONFIG.clientKey}`);
    
  } else {
    console.log(`❌ 没有找到可用的配置`);
    console.log(`❌ 请检查服务器状态和网络连接`);
    
    console.log(`\n🔧 故障排除步骤:`);
    console.log(`   1. 确认服务器运行: curl ${CONFIG.baseURL}/health`);
    console.log(`   2. 检查网络连接`);
    console.log(`   3. 验证Parse Server配置`);
    console.log(`   4. 检查防火墙设置`);
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, CONFIG, CLIENT_CONFIGS };

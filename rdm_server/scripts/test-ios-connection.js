#!/usr/bin/env node

/**
 * iOS 连接测试脚本
 * 验证 iOS 客户端能否正常连接到服务器
 */

require('dotenv').config();
const Parse = require('parse/node');

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function colorize(color, text) {
  return `${colors[color]}${text}${colors.reset}`;
}

// 测试配置
const testConfigs = [
  {
    name: '本地开发环境',
    serverURL: 'http://10.0.136.252:3000/parse',
    appId: 'taskflow-app-id',
    jsKey: 'taskflow-js-key'
  },
  {
    name: '生产环境',
    serverURL: 'https://timescale.sanva.tk/parse',
    appId: 'taskflow-app-id',
    jsKey: 'taskflow-js-key'
  }
];

// 测试账号
const testAccounts = [
  {
    name: '管理员账户',
    username: 'admin',
    password: 'Test123!',
    email: '<EMAIL>'
  },
  {
    name: '测试用户',
    username: 'test_user',
    password: 'Test123!',
    email: '<EMAIL>'
  },
  {
    name: '演示用户',
    username: 'demo_user',
    password: 'Test123!',
    email: '<EMAIL>'
  }
];

/**
 * 测试服务器连接
 */
async function testServerConnection(config) {
  try {
    console.log(colorize('blue', `\n🔗 测试 ${config.name}...`));
    console.log(`   服务器: ${config.serverURL}`);
    
    // 初始化 Parse
    Parse.initialize(config.appId, config.jsKey);
    Parse.serverURL = config.serverURL;
    
    // 测试基本连接
    const TestObject = Parse.Object.extend('ConnectionTest');
    const testObj = new TestObject();
    testObj.set('test', true);
    testObj.set('timestamp', new Date());
    
    await testObj.save();
    await testObj.destroy();
    
    console.log(colorize('green', '   ✅ 服务器连接正常'));
    return true;
    
  } catch (error) {
    console.log(colorize('red', `   ❌ 连接失败: ${error.message}`));
    return false;
  }
}

/**
 * 测试用户登录
 */
async function testUserLogin(config, account) {
  try {
    console.log(colorize('blue', `\n👤 测试 ${account.name} 登录...`));
    console.log(`   用户名: ${account.username}`);
    console.log(`   邮箱: ${account.email}`);
    
    // 初始化 Parse
    Parse.initialize(config.appId, config.jsKey);
    Parse.serverURL = config.serverURL;
    
    // 尝试登录
    const user = await Parse.User.logIn(account.username, account.password);
    
    console.log(colorize('green', '   ✅ 登录成功'));
    console.log(`   用户ID: ${user.id}`);
    console.log(`   会话令牌: ${user.getSessionToken().substring(0, 20)}...`);
    
    // 登出
    await Parse.User.logOut();
    
    return true;
    
  } catch (error) {
    console.log(colorize('red', `   ❌ 登录失败: ${error.message}`));
    return false;
  }
}

/**
 * 测试数据查询
 */
async function testDataQuery(config, account) {
  try {
    console.log(colorize('blue', `\n📊 测试数据查询...`));
    
    // 初始化 Parse
    Parse.initialize(config.appId, config.jsKey);
    Parse.serverURL = config.serverURL;
    
    // 登录
    const user = await Parse.User.logIn(account.username, account.password);
    
    // 查询任务
    const taskQuery = new Parse.Query('Task');
    taskQuery.equalTo('user', user);
    taskQuery.limit(5);
    const tasks = await taskQuery.find();
    
    // 查询习惯
    const habitQuery = new Parse.Query('Habit');
    habitQuery.equalTo('user', user);
    habitQuery.limit(5);
    const habits = await habitQuery.find();
    
    console.log(colorize('green', '   ✅ 数据查询成功'));
    console.log(`   任务数量: ${tasks.length}`);
    console.log(`   习惯数量: ${habits.length}`);
    
    // 登出
    await Parse.User.logOut();
    
    return true;
    
  } catch (error) {
    console.log(colorize('red', `   ❌ 数据查询失败: ${error.message}`));
    return false;
  }
}

/**
 * 生成 iOS 配置代码
 */
function generateiOSConfig() {
  console.log(colorize('blue', '\n📱 iOS 配置代码:'));
  
  console.log(colorize('yellow', '\n// AppConfig.swift'));
  console.log(`
import Foundation

enum Environment {
    case development
    case production
}

class AppConfig {
    static let shared = AppConfig()
    
    #if DEBUG
    let environment: Environment = .development
    #else
    let environment: Environment = .production
    #endif
    
    var parseServerURL: String {
        switch environment {
        case .development:
            return "http://10.0.136.252:3000/parse"
        case .production:
            return "https://timescale.sanva.tk/parse"
        }
    }
    
    var applicationId: String {
        return "taskflow-app-id"
    }
    
    var clientKey: String {
        return "taskflow-js-key"
    }
}
  `);
  
  console.log(colorize('yellow', '\n// Parse 初始化代码'));
  console.log(`
let configuration = ParseClientConfiguration {
    $0.applicationId = AppConfig.shared.applicationId
    $0.clientKey = AppConfig.shared.clientKey
    $0.server = AppConfig.shared.parseServerURL
}
Parse.initialize(with: configuration)
  `);
  
  console.log(colorize('yellow', '\n// 测试登录代码'));
  console.log(`
PFUser.logInWithUsername(inBackground: "test_user", password: "Test123!") { (user, error) in
    if let error = error {
        print("登录失败: \\(error.localizedDescription)")
    } else if let user = user {
        print("登录成功: \\(user.username ?? "")")
    }
}
  `);
}

/**
 * 主函数
 */
async function main() {
  console.log(colorize('blue', '🧪 TaskFlow iOS 连接测试\n'));
  console.log('=' .repeat(50));
  
  let totalTests = 0;
  let passedTests = 0;
  
  // 测试每个配置
  for (const config of testConfigs) {
    // 测试服务器连接
    totalTests++;
    if (await testServerConnection(config)) {
      passedTests++;
      
      // 如果连接成功，测试用户登录
      for (const account of testAccounts) {
        totalTests++;
        if (await testUserLogin(config, account)) {
          passedTests++;
          
          // 测试数据查询（只对第一个账户）
          if (account.username === 'test_user') {
            totalTests++;
            if (await testDataQuery(config, account)) {
              passedTests++;
            }
          }
        }
      }
    }
    
    console.log('\n' + '-'.repeat(50));
  }
  
  // 显示测试结果
  console.log(colorize('blue', '\n📊 测试结果:'));
  console.log(`总测试数: ${totalTests}`);
  console.log(`通过: ${colorize('green', passedTests)}`);
  console.log(`失败: ${colorize('red', totalTests - passedTests)}`);
  console.log(`通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log(colorize('green', '\n🎉 所有测试通过！'));
  } else {
    console.log(colorize('yellow', '\n⚠️  部分测试失败，请检查配置'));
  }
  
  // 生成 iOS 配置
  generateiOSConfig();
  
  console.log(colorize('blue', '\n📋 测试账号信息:'));
  testAccounts.forEach(account => {
    console.log(`${account.name}: ${account.username} / ${account.password}`);
  });
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error(colorize('red', '测试运行失败:'), error);
    process.exit(1);
  });
}

module.exports = { testServerConnection, testUserLogin, testDataQuery };

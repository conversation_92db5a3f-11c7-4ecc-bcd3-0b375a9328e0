# TaskFlow iOS 开发配置指南

## 🔐 测试账号信息

### 预定义测试账号
| 角色 | 用户名 | 邮箱 | 密码 | 权限 |
|------|--------|------|------|------|
| 管理员 | `admin` | `<EMAIL>` | `Test123!` | 管理员权限 |
| 测试用户 | `test_user` | `<EMAIL>` | `Test123!` | 普通用户 |
| 演示用户 | `demo_user` | `<EMAIL>` | `Test123!` | 普通用户 |

### 随机生成的用户
- **邮箱格式**: `user{N}@taskflow.test` (如: <EMAIL>, <EMAIL>)
- **用户名格式**: `user_{随机字符串}` (如: user_abc123)
- **密码**: `Test123!` (所有用户统一密码)

## 🌐 网络配置

### 开发环境 (本地网络)
```swift
// Parse SDK 配置
let configuration = ParseClientConfiguration {
    $0.applicationId = "taskflow-app-id"
    $0.clientKey = "taskflow-js-key"
    $0.server = "http://************:3000/parse"
}
Parse.initialize(with: configuration)
```

### 生产环境 (线上服务)
```swift
// Parse SDK 配置
let configuration = ParseClientConfiguration {
    $0.applicationId = "taskflow-app-id"
    $0.clientKey = "taskflow-js-key"
    $0.server = "https://timescale.sanva.tk/parse"
}
Parse.initialize(with: configuration)
```

## 📱 iOS 项目配置

### 1. Info.plist 配置
为了在开发时访问本地网络，需要在 `Info.plist` 中添加以下配置：

```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
    <key>NSExceptionDomains</key>
    <dict>
        <key>************</key>
        <dict>
            <key>NSExceptionAllowsInsecureHTTPLoads</key>
            <true/>
            <key>NSExceptionMinimumTLSVersion</key>
            <string>TLSv1.0</string>
        </dict>
        <key>localhost</key>
        <dict>
            <key>NSExceptionAllowsInsecureHTTPLoads</key>
            <true/>
            <key>NSExceptionMinimumTLSVersion</key>
            <string>TLSv1.0</string>
        </dict>
    </dict>
</dict>
```

### 2. 环境切换配置
建议创建一个配置管理类：

```swift
import Foundation

enum Environment {
    case development
    case production
}

class AppConfig {
    static let shared = AppConfig()
    
    #if DEBUG
    let environment: Environment = .development
    #else
    let environment: Environment = .production
    #endif
    
    var parseServerURL: String {
        switch environment {
        case .development:
            return "http://************:3000/parse"
        case .production:
            return "https://timescale.sanva.tk/parse"
        }
    }
    
    var applicationId: String {
        return "taskflow-app-id"
    }
    
    var clientKey: String {
        return "taskflow-js-key"
    }
}
```

### 3. Parse SDK 初始化
在 `AppDelegate.swift` 或 `SceneDelegate.swift` 中：

```swift
import Parse

func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
    
    let configuration = ParseClientConfiguration {
        $0.applicationId = AppConfig.shared.applicationId
        $0.clientKey = AppConfig.shared.clientKey
        $0.server = AppConfig.shared.parseServerURL
    }
    
    Parse.initialize(with: configuration)
    
    return true
}
```

## 🧪 测试登录示例

```swift
import Parse

class AuthService {
    static func loginWithTestAccount(completion: @escaping (Bool, Error?) -> Void) {
        PFUser.logInWithUsername(inBackground: "test_user", password: "Test123!") { (user, error) in
            if let error = error {
                print("登录失败: \(error.localizedDescription)")
                completion(false, error)
            } else if let user = user {
                print("登录成功: \(user.username ?? "")")
                completion(true, nil)
            }
        }
    }
    
    static func loginWithAdminAccount(completion: @escaping (Bool, Error?) -> Void) {
        PFUser.logInWithUsername(inBackground: "admin", password: "Test123!") { (user, error) in
            if let error = error {
                print("管理员登录失败: \(error.localizedDescription)")
                completion(false, error)
            } else if let user = user {
                print("管理员登录成功: \(user.username ?? "")")
                completion(true, nil)
            }
        }
    }
}
```

## 🔧 服务器启动命令

### 开发环境启动
```bash
# 进入服务器目录
cd rdm_server

# 启动简化版服务器（支持网络访问）
node server-simple.js

# 或者使用 nodemon 自动重启
npx nodemon server-simple.js
```

### 验证服务器状态
```bash
# 检查健康状态
curl http://************:3000/health

# 测试 Parse API
curl -X POST \
  -H "X-Parse-Application-Id: taskflow-app-id" \
  -H "Content-Type: application/json" \
  -d '{"username":"test_user","password":"Test123!"}' \
  http://************:3000/parse/login
```

## 📊 数据管理命令

```bash
# 生成测试数据
npm run seed:small    # 3用户 + 15任务 + 6习惯
npm run seed:medium   # 10用户 + 165任务 + 56习惯
npm run seed:large    # 20用户 + 500任务 + 160习惯

# 清理测试数据
npm run seed:clean

# 运行连接测试
npm run seed:test
```

## 🚨 注意事项

1. **网络权限**: 确保 iOS 设备和服务器在同一网络下
2. **防火墙**: 检查 macOS 防火墙设置，确保允许端口 3000 的连接
3. **IP 地址**: 如果 IP 地址变化，需要更新配置
4. **HTTPS**: 生产环境必须使用 HTTPS
5. **安全**: 测试密码仅用于开发，生产环境需要更强的密码策略

## 🔗 相关端点

- **开发服务器**: http://************:3000
- **Parse API**: http://************:3000/parse
- **健康检查**: http://************:3000/health
- **生产服务器**: https://timescale.sanva.tk
- **生产 API**: https://timescale.sanva.tk/parse

## 📱 推荐的开发流程

1. 启动本地服务器
2. 运行数据填充脚本生成测试数据
3. 在 iOS 模拟器中测试应用
4. 使用测试账号进行功能验证
5. 准备发布时切换到生产环境配置

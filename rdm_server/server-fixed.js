/**
 * TaskFlow 修复版服务器
 * 解决Parse Server认证问题
 */

require('dotenv').config();
const express = require('express');
const ParseServer = require('parse-server').ParseServer;
const mongoose = require('mongoose');
const session = require('express-session');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Parse Server 配置
const parseConfig = {
  databaseURI: process.env.MONGODB_URI || 'mongodb://localhost:27018/taskflow',
  appId: process.env.PARSE_APP_ID || 'taskflow-app-id',
  masterKey: process.env.PARSE_MASTER_KEY || 'taskflow-master-key',
  javascriptKey: process.env.PARSE_JAVASCRIPT_KEY || 'taskflow-js-key',
  restAPIKey: process.env.PARSE_REST_API_KEY || 'taskflow-rest-key',
  serverURL: process.env.PARSE_SERVER_URL || `http://************:${PORT}/parse`,
  mountPath: '/parse',
  
  // 基础配置
  enableAnonymousUsers: false,
  allowClientClassCreation: true,
  maxUploadSize: '10mb',

  // 客户端密钥配置 (重要!)
  clientKey: process.env.PARSE_JAVASCRIPT_KEY || 'taskflow-js-key',
  
  // 安全配置
  sessionLength: 604800, // 7 days
  revokeSessionOnPasswordReset: true,
  
  // 日志配置
  logLevel: 'info',
  silent: false,
  
  // 开发环境配置
  verifyUserEmails: false,
  preventLoginWithUnverifiedEmail: false,
  
  // 修复配置 - 禁用可能导致问题的功能
  directAccess: false,
  
  // 文件适配器配置
  filesAdapter: {
    module: '@parse/fs-files-adapter',
    options: {
      filesSubDirectory: 'files'
    }
  }
};

// 基础中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 会话中间件
app.use(session({
  secret: process.env.SESSION_SECRET || 'taskflow-admin-secret',
  resave: false,
  saveUninitialized: false,
  cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 } // 24小时
}));

// 设置模板引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'admin/views'));

// 静态文件服务
app.use('/admin', express.static(path.join(__dirname, 'admin/public')));

// 请求日志中间件
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.url}`);
  console.log(`  Headers:`, {
    'user-agent': req.headers['user-agent'],
    'content-type': req.headers['content-type'],
    'x-parse-application-id': req.headers['x-parse-application-id'],
    'x-parse-rest-api-key': req.headers['x-parse-rest-api-key'],
    'x-parse-session-token': req.headers['x-parse-session-token'] ? 'present' : 'missing'
  });
  if (req.body && Object.keys(req.body).length > 0) {
    console.log(`  Body:`, req.body);
  }
  next();
});

// CORS 配置
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Parse-Application-Id, X-Parse-REST-API-Key, X-Parse-Session-Token, X-Parse-Master-Key, X-Parse-Client-Key');

  if (req.method === 'OPTIONS') {
    console.log(`[CORS] OPTIONS request for ${req.url}`);
    res.sendStatus(200);
  } else {
    next();
  }
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    mongodb: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
    parseServer: 'running'
  });
});

// 调试端点 - 显示所有可用路由
app.get('/debug/routes', (req, res) => {
  const routes = [];

  app._router.stack.forEach(function(middleware) {
    if (middleware.route) {
      routes.push({
        path: middleware.route.path,
        methods: Object.keys(middleware.route.methods)
      });
    } else if (middleware.name === 'router') {
      middleware.handle.stack.forEach(function(handler) {
        if (handler.route) {
          routes.push({
            path: handler.route.path,
            methods: Object.keys(handler.route.methods)
          });
        }
      });
    }
  });

  res.json({
    message: 'TaskFlow Debug - Available Routes',
    routes: routes,
    parseConfig: {
      appId: parseConfig.appId,
      serverURL: parseConfig.serverURL,
      mountPath: parseConfig.mountPath
    },
    timestamp: new Date().toISOString()
  });
});

// Parse Server 状态检查
app.get('/debug/parse', (req, res) => {
  res.json({
    message: 'Parse Server Debug Info',
    config: {
      appId: parseConfig.appId,
      serverURL: parseConfig.serverURL,
      mountPath: parseConfig.mountPath,
      databaseURI: parseConfig.databaseURI.replace(/\/\/.*@/, '//***:***@') // 隐藏密码
    },
    endpoints: {
      login: `${parseConfig.serverURL}/login`,
      classes: `${parseConfig.serverURL}/classes`,
      users: `${parseConfig.serverURL}/users`,
      functions: `${parseConfig.serverURL}/functions`
    },
    headers: {
      required: [
        'X-Parse-Application-Id',
        'X-Parse-REST-API-Key (for REST)',
        'X-Parse-Client-Key (for SDK)',
        'Content-Type: application/json'
      ]
    },
    testUser: {
      username: 'demo_user',
      password: 'Test123!'
    },
    timestamp: new Date().toISOString()
  });
});

// 管理后台路由（使用真实数据）
const adminRoutes = require('./admin/routes/index');
app.use('/admin', adminRoutes);

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: 'TaskFlow API Server (Fixed)',
    version: '1.0.0',
    endpoints: {
      parse: '/parse',
      health: '/health',
      admin: '/admin',
      api: '/api/v1'
    }
  });
});

// 客户端配置测试端点
app.post('/debug/client-test', async (req, res) => {
  console.log('\n🔍 客户端测试请求');
  console.log('Headers:', req.headers);
  console.log('Body:', req.body);

  const appId = req.headers['x-parse-application-id'];
  const restKey = req.headers['x-parse-rest-api-key'];
  const clientKey = req.headers['x-parse-client-key'];

  const response = {
    timestamp: new Date().toISOString(),
    request: {
      method: req.method,
      url: req.url,
      headers: {
        'x-parse-application-id': appId || 'missing',
        'x-parse-rest-api-key': restKey || 'missing',
        'x-parse-client-key': clientKey || 'missing',
        'content-type': req.headers['content-type'] || 'missing'
      },
      body: req.body
    },
    validation: {
      appId: appId === parseConfig.appId ? '✅ correct' : '❌ incorrect',
      restKey: restKey === parseConfig.restAPIKey ? '✅ correct' : '❌ incorrect',
      clientKey: clientKey === parseConfig.javascriptKey ? '✅ correct' : '❌ incorrect'
    },
    recommendations: []
  };

  // 添加建议
  if (!appId) {
    response.recommendations.push('添加 X-Parse-Application-Id 头');
  }
  if (!restKey && !clientKey) {
    response.recommendations.push('添加 X-Parse-REST-API-Key 或 X-Parse-Client-Key 头');
  }
  if (req.headers['content-type'] !== 'application/json') {
    response.recommendations.push('设置 Content-Type: application/json');
  }

  res.json(response);
});

// 测试登录端点
app.post('/test-login', async (req, res) => {
  console.log('\n🔐 测试登录请求');
  console.log('Headers:', req.headers);
  console.log('Body:', req.body);

  try {
    const { username, password } = req.body;

    // 使用Parse SDK进行登录测试
    const Parse = require('parse/node');
    Parse.initialize(parseConfig.appId, parseConfig.javascriptKey, parseConfig.masterKey);
    Parse.serverURL = parseConfig.serverURL;

    const user = await Parse.User.logIn(username, password);

    console.log('✅ 登录成功:', user.get('username'));

    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.get('username'),
        email: user.get('email'),
        sessionToken: user.getSessionToken()
      }
    });
  } catch (error) {
    console.log('❌ 登录失败:', error.message);
    res.status(401).json({
      success: false,
      error: error.message
    });
  }
});

// 启动服务器
async function startServer() {
  try {
    console.log('🔌 连接 MongoDB...');
    
    // 连接 MongoDB
    await mongoose.connect(parseConfig.databaseURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    console.log('✅ MongoDB 连接成功');
    
    // 创建 Parse Server
    console.log('🚀 启动 Parse Server...');
    const parseServer = new ParseServer(parseConfig);
    
    // 等待 Parse Server 初始化
    await parseServer.start();
    
    // 挂载 Parse Server
    app.use(parseConfig.mountPath, parseServer.app);
    
    console.log('✅ Parse Server 启动成功');
    
    // 启动服务器
    app.listen(PORT, '0.0.0.0', () => {
      console.log('🚀 TaskFlow 服务器启动成功!');
      console.log(`📍 服务器地址: http://localhost:${PORT}`);
      console.log(`🔗 Parse Server: http://localhost:${PORT}${parseConfig.mountPath}`);
      console.log(`🔗 网络地址: http://************:${PORT}${parseConfig.mountPath}`);
      console.log(`💚 健康检查: http://localhost:${PORT}/health`);
      console.log(`🔧 管理后台: http://localhost:${PORT}/admin`);
      console.log('');
      console.log('📊 测试用户凭据:');
      console.log('  用户名: demo_user');
      console.log('  邮箱: <EMAIL>');
      console.log('  密码: Test123!');
      console.log('');
      console.log('🧪 测试登录:');
      console.log(`  curl -X POST http://localhost:${PORT}/test-login \\`);
      console.log(`    -H "Content-Type: application/json" \\`);
      console.log(`    -d '{"username":"demo_user","password":"Test123!"}'`);
    });
    
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 正在关闭服务器...');
  
  try {
    await mongoose.disconnect();
    console.log('✅ MongoDB 连接已关闭');
    process.exit(0);
  } catch (error) {
    console.error('❌ 关闭过程中出错:', error);
    process.exit(1);
  }
});

// 启动服务器
startServer();

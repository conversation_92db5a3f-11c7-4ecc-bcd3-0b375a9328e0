{"code":119,"level":"error","message":"This user is not allowed to access non-existent class: ConnectionTest","stack":"Error: This user is not allowed to access non-existent class: ConnectionTest\n    at /Users/<USER>/gitlab1/rdm_all/rdm_server/node_modules/parse-server/lib/RestWrite.js:174:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-08-25T03:40:13.467Z"}
{"level":"error","message":"Request using master key rejected as the request IP address '************' is not set in Parse Server option 'masterKeyIps'.","timestamp":"2025-08-25T09:26:58.113Z"}
{"code":202,"level":"error","message":"Account already exists for this username.","stack":"Error: Account already exists for this username.\n    at /Users/<USER>/gitlab1/rdm_all/rdm_server/node_modules/parse-server/lib/RestWrite.js:608:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-25T09:26:58.186Z"}
{"level":"error","message":"Request using master key rejected as the request IP address '************' is not set in Parse Server option 'masterKeyIps'.","timestamp":"2025-08-25T09:26:58.188Z"}
{"code":202,"level":"error","message":"Account already exists for this username.","stack":"Error: Account already exists for this username.\n    at /Users/<USER>/gitlab1/rdm_all/rdm_server/node_modules/parse-server/lib/RestWrite.js:608:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-25T09:26:58.245Z"}
{"level":"error","message":"Request using master key rejected as the request IP address '************' is not set in Parse Server option 'masterKeyIps'.","timestamp":"2025-08-25T09:26:58.247Z"}
{"code":202,"level":"error","message":"Account already exists for this username.","stack":"Error: Account already exists for this username.\n    at /Users/<USER>/gitlab1/rdm_all/rdm_server/node_modules/parse-server/lib/RestWrite.js:608:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-25T09:26:58.356Z"}
{"level":"error","message":"Request using master key rejected as the request IP address '************' is not set in Parse Server option 'masterKeyIps'.","timestamp":"2025-08-25T09:26:58.359Z"}
{"level":"error","message":"Request using master key rejected as the request IP address '************' is not set in Parse Server option 'masterKeyIps'.","timestamp":"2025-08-25T09:26:58.361Z"}
{"level":"error","message":"Request using master key rejected as the request IP address '************' is not set in Parse Server option 'masterKeyIps'.","timestamp":"2025-08-25T09:26:58.362Z"}
{"code":101,"level":"error","message":"Invalid username/password.","stack":"Error: Invalid username/password.\n    at /Users/<USER>/gitlab1/rdm_all/rdm_server/node_modules/parse-server/lib/Routers/UsersRouter.js:116:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-25T09:30:16.857Z"}
{"level":"error","message":"Invalid key(s) found in Parse Server configuration: apiVersion","timestamp":"2025-08-25T09:35:12.243Z"}
{"code":202,"level":"error","message":"Account already exists for this username.","stack":"Error: Account already exists for this username.\n    at /Users/<USER>/gitlab1/rdm_all/rdm_server/node_modules/parse-server/lib/RestWrite.js:608:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-25T09:37:31.721Z"}
{"level":"error","message":"Request using master key rejected as the request IP address '************' is not set in Parse Server option 'masterKeyIps'.","timestamp":"2025-08-25T10:01:20.191Z"}

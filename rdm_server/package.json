{"name": "taskflow-server", "version": "1.0.0", "description": "TaskFlow backend server with Express.js and Parse Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "seed": "node scripts/seed-data-simple.js", "seed:clean": "node scripts/seed-data-simple.js --clean", "seed:test": "node scripts/test-seed.js", "seed:small": "node scripts/seed-data-simple.js --users=3 --tasks=5 --habits=2", "seed:medium": "node scripts/seed-data-simple.js --users=10 --tasks=15 --habits=5", "seed:large": "node scripts/seed-data-simple.js --users=20 --tasks=25 --habits=8"}, "keywords": ["taskflow", "express", "parse-server", "mongodb", "rest-api"], "author": "TaskFlow Team", "license": "MIT", "dependencies": {"@parse/fs-files-adapter": "^3.0.0", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "parse-dashboard": "^7.3.0", "parse-server": "^8.2.3", "redis": "^4.6.10", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "chalk": "^4.1.2", "ejs": "^3.1.10", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "express-session": "^1.18.2", "faker": "^5.5.3", "inquirer": "^8.2.6", "jest": "^29.7.0", "nodemon": "^3.0.2", "ora": "^5.4.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/taskflow/taskflow-server.git"}, "bugs": {"url": "https://github.com/taskflow/taskflow-server/issues"}, "homepage": "https://github.com/taskflow/taskflow-server#readme"}
/**
 * TaskFlow 服务器 - 包含API和Parse Server
 */

require('dotenv').config();
const express = require('express');
const ParseServer = require('parse-server').ParseServer;
const mongoose = require('mongoose');
const session = require('express-session');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Parse Server 配置
const parseConfig = {
  databaseURI: process.env.MONGODB_URI || 'mongodb://localhost:27018/taskflow',
  appId: process.env.PARSE_APP_ID || 'taskflow-app-id',
  masterKey: process.env.PARSE_MASTER_KEY || 'taskflow-master-key',
  javascriptKey: process.env.PARSE_JAVASCRIPT_KEY || 'taskflow-js-key',
  restAPIKey: process.env.PARSE_REST_API_KEY || 'taskflow-rest-key',
  serverURL: process.env.PARSE_SERVER_URL || `http://************:${PORT}/parse`,
  mountPath: '/parse',
  
  // 基础配置
  enableAnonymousUsers: false,
  allowClientClassCreation: true,
  maxUploadSize: '10mb',
  
  // 客户端密钥配置
  clientKey: process.env.PARSE_JAVASCRIPT_KEY || 'taskflow-js-key',
  
  // 安全配置
  sessionLength: 604800, // 7 days
  revokeSessionOnPasswordReset: true,
  
  // 日志配置
  logLevel: 'info',
  silent: false,
  
  // 开发环境配置
  verifyUserEmails: false,
  preventLoginWithUnverifiedEmail: false,

  // 修复配置
  directAccess: false,

  // 允许所有IP使用master key (仅开发环境)
  masterKeyIps: ['0.0.0.0/0'],
  
  // 文件适配器配置
  filesAdapter: {
    module: '@parse/fs-files-adapter',
    options: {
      filesSubDirectory: 'files'
    }
  }
};

// 基础中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 会话中间件
app.use(session({
  secret: process.env.SESSION_SECRET || 'taskflow-admin-secret',
  resave: false,
  saveUninitialized: false,
  cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 }
}));

// 设置模板引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'admin/views'));

// 静态文件服务
app.use('/admin', express.static(path.join(__dirname, 'admin/public')));

// 请求日志中间件
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.url}`);
  if (req.headers['x-parse-application-id'] || req.headers.authorization) {
    console.log(`  Auth Headers:`, {
      'x-parse-application-id': req.headers['x-parse-application-id'],
      'x-parse-client-key': req.headers['x-parse-client-key'],
      'x-parse-rest-api-key': req.headers['x-parse-rest-api-key'],
      'authorization': req.headers.authorization ? 'present' : 'missing'
    });
  }
  if (req.body && Object.keys(req.body).length > 0) {
    console.log(`  Body:`, req.body);
  }
  next();
});

// CORS 配置
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Parse-Application-Id, X-Parse-REST-API-Key, X-Parse-Session-Token, X-Parse-Master-Key, X-Parse-Client-Key');
  
  if (req.method === 'OPTIONS') {
    console.log(`[CORS] OPTIONS request for ${req.url}`);
    res.sendStatus(200);
  } else {
    next();
  }
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    mongodb: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
    parseServer: 'running'
  });
});

// 简化的API v1 认证端点
app.post('/api/v1/auth/login', async (req, res) => {
  console.log('\n🔐 API v1 登录请求');
  console.log('Headers:', req.headers);
  console.log('Body:', req.body);
  
  try {
    const { email, password, username } = req.body;
    const loginField = email || username;
    
    if (!loginField || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email/username and password are required'
      });
    }
    
    // 使用Parse SDK进行登录
    const Parse = require('parse/node');
    Parse.initialize(parseConfig.appId, parseConfig.javascriptKey, parseConfig.masterKey);
    Parse.serverURL = parseConfig.serverURL;
    
    let user;
    
    // 尝试用邮箱登录
    if (email) {
      const query = new Parse.Query(Parse.User);
      query.equalTo('email', email);
      const userObj = await query.first({ useMasterKey: true });
      
      if (userObj) {
        // 找到用户，用用户名登录
        user = await Parse.User.logIn(userObj.get('username'), password);
      } else {
        throw new Error('User not found');
      }
    } else {
      // 直接用用户名登录
      user = await Parse.User.logIn(username, password);
    }
    
    console.log('✅ API v1 登录成功:', user.get('username'));
    
    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.get('username'),
        email: user.get('email'),
        fullName: user.get('fullName'),
        sessionToken: user.getSessionToken()
      }
    });
    
  } catch (error) {
    console.log('❌ API v1 登录失败:', error.message);
    res.status(401).json({
      success: false,
      error: error.message
    });
  }
});

// 简化的API v1 用户信息端点
app.get('/api/v1/auth/me', async (req, res) => {
  console.log('\n👤 API v1 用户信息请求');
  
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Authorization token required'
      });
    }
    
    const Parse = require('parse/node');
    Parse.initialize(parseConfig.appId, parseConfig.javascriptKey, parseConfig.masterKey);
    Parse.serverURL = parseConfig.serverURL;
    
    const user = await Parse.User.become(token);
    
    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.get('username'),
        email: user.get('email'),
        fullName: user.get('fullName')
      }
    });
    
  } catch (error) {
    console.log('❌ API v1 用户信息失败:', error.message);
    res.status(401).json({
      success: false,
      error: error.message
    });
  }
});

// 管理后台路由
const adminRoutes = require('./admin/routes/index');
app.use('/admin', adminRoutes);

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: 'TaskFlow API Server (With API)',
    version: '1.0.0',
    endpoints: {
      parse: '/parse',
      health: '/health',
      admin: '/admin',
      api: '/api/v1'
    }
  });
});

// 启动服务器
async function startServer() {
  try {
    console.log('🔌 连接 MongoDB...');
    
    await mongoose.connect(parseConfig.databaseURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    console.log('✅ MongoDB 连接成功');
    
    console.log('🚀 启动 Parse Server...');
    const parseServer = new ParseServer(parseConfig);
    
    await parseServer.start();
    
    app.use(parseConfig.mountPath, parseServer.app);
    
    console.log('✅ Parse Server 启动成功');
    
    app.listen(PORT, '0.0.0.0', () => {
      console.log('🚀 TaskFlow 服务器启动成功!');
      console.log(`📍 服务器地址: http://localhost:${PORT}`);
      console.log(`🔗 Parse Server: http://localhost:${PORT}${parseConfig.mountPath}`);
      console.log(`🔗 API v1: http://localhost:${PORT}/api/v1`);
      console.log(`🔗 网络地址: http://************:${PORT}`);
      console.log(`💚 健康检查: http://localhost:${PORT}/health`);
      console.log(`🔧 管理后台: http://localhost:${PORT}/admin`);
      console.log('');
      console.log('📊 测试用户凭据:');
      console.log('  用户名: demo_user');
      console.log('  邮箱: <EMAIL>');
      console.log('  密码: Test123!');
      console.log('');
      console.log('🧪 测试API v1登录:');
      console.log(`  curl -X POST http://localhost:${PORT}/api/v1/auth/login \\`);
      console.log(`    -H "Content-Type: application/json" \\`);
      console.log(`    -d '{"email":"<EMAIL>","password":"Test123!"}'`);
    });
    
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 正在关闭服务器...');
  
  try {
    await mongoose.disconnect();
    console.log('✅ MongoDB 连接已关闭');
    process.exit(0);
  } catch (error) {
    console.error('❌ 关闭过程中出错:', error);
    process.exit(1);
  }
});

startServer();

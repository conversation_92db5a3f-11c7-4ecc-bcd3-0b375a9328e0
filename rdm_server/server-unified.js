/**
 * TaskFlow 统一服务器
 * 包含标准化数据模型、错误处理和开发工具
 */

require('dotenv').config();
const express = require('express');
const ParseServer = require('parse-server').ParseServer;
const mongoose = require('mongoose');
const session = require('express-session');
const path = require('path');
const crypto = require('crypto');

// 导入中间件和模型
const { responseHandler, errorHandler, notFoundHandler, DevModeFeatures } = require('./src/middleware/response');
const { Validators } = require('./src/middleware/validation');
const { DataTransformers } = require('./src/models');

const app = express();
const PORT = process.env.PORT || 3000;

// Parse Server 配置
const parseConfig = {
  databaseURI: process.env.MONGODB_URI || 'mongodb://localhost:27018/taskflow',
  appId: process.env.PARSE_APP_ID || 'taskflow-app-id',
  masterKey: process.env.PARSE_MASTER_KEY || 'taskflow-master-key',
  javascriptKey: process.env.PARSE_JAVASCRIPT_KEY || 'taskflow-js-key',
  restAPIKey: process.env.PARSE_REST_API_KEY || 'taskflow-rest-key',
  serverURL: process.env.PARSE_SERVER_URL || `http://************:${PORT}/parse`,
  mountPath: '/parse',
  
  // 基础配置
  enableAnonymousUsers: false,
  allowClientClassCreation: true,
  maxUploadSize: '10mb',
  clientKey: process.env.PARSE_JAVASCRIPT_KEY || 'taskflow-js-key',
  
  // 安全配置
  sessionLength: 604800, // 7 days
  revokeSessionOnPasswordReset: true,
  masterKeyIps: ['0.0.0.0/0'], // 开发环境允许所有IP
  
  // 日志配置
  logLevel: 'info',
  silent: false,
  
  // 开发环境配置
  verifyUserEmails: false,
  preventLoginWithUnverifiedEmail: false,
  directAccess: false,
  
  // 文件适配器配置
  filesAdapter: {
    module: '@parse/fs-files-adapter',
    options: {
      filesSubDirectory: 'files'
    }
  }
};

// 基础中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 会话中间件
app.use(session({
  secret: process.env.SESSION_SECRET || 'taskflow-admin-secret',
  resave: false,
  saveUninitialized: false,
  cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 }
}));

// 请求ID中间件
app.use((req, res, next) => {
  req.id = crypto.randomUUID();
  res.setHeader('X-Request-ID', req.id);
  next();
});

// 设置模板引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'admin/views'));

// 静态文件服务
app.use('/admin', express.static(path.join(__dirname, 'admin/public')));

// 请求日志中间件
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.url} (${req.id})`);
  
  if (req.headers['x-parse-application-id'] || req.headers.authorization) {
    console.log(`  Auth:`, {
      'app-id': req.headers['x-parse-application-id'] ? 'present' : 'missing',
      'client-key': req.headers['x-parse-client-key'] ? 'present' : 'missing',
      'rest-key': req.headers['x-parse-rest-api-key'] ? 'present' : 'missing',
      'auth': req.headers.authorization ? 'present' : 'missing'
    });
  }
  
  if (req.body && Object.keys(req.body).length > 0) {
    const logBody = { ...req.body };
    if (logBody.password) logBody.password = '***';
    console.log(`  Body:`, logBody);
  }
  
  next();
});

// CORS 配置
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Parse-Application-Id, X-Parse-REST-API-Key, X-Parse-Session-Token, X-Parse-Master-Key, X-Parse-Client-Key');
  
  if (req.method === 'OPTIONS') {
    console.log(`[CORS] OPTIONS request for ${req.url}`);
    res.sendStatus(200);
  } else {
    next();
  }
});

// 响应处理中间件
app.use(responseHandler);

// 健康检查端点
app.get('/health', (req, res) => {
  res.success({
    status: 'ok',
    mongodb: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
    parseServer: 'running',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// 开发模式端点
if (process.env.NODE_ENV === 'development') {
  // 获取保存的凭据
  app.get('/dev/credentials', (req, res) => {
    res.success(DevModeFeatures.getSavedCredentials(), '获取保存的凭据成功');
  });
  
  // 快速登录
  app.get('/dev/credentials/:account', (req, res) => {
    DevModeFeatures.quickLogin(req, res);
  });
  
  // 数据模型文档
  app.get('/dev/models', (req, res) => {
    res.success({
      endpoints: {
        auth: {
          login: 'POST /api/v1/auth/login',
          me: 'GET /api/v1/auth/me'
        },
        tasks: {
          list: 'GET /api/v1/tasks',
          create: 'POST /api/v1/tasks',
          get: 'GET /api/v1/tasks/:id',
          update: 'PUT /api/v1/tasks/:id',
          delete: 'DELETE /api/v1/tasks/:id'
        },
        habits: {
          list: 'GET /api/v1/habits',
          create: 'POST /api/v1/habits',
          get: 'GET /api/v1/habits/:id',
          update: 'PUT /api/v1/habits/:id',
          delete: 'DELETE /api/v1/habits/:id'
        }
      },
      responseFormat: {
        success: {
          success: true,
          timestamp: '2024-01-01T00:00:00.000Z',
          requestId: 'uuid',
          data: 'any'
        },
        error: {
          success: false,
          timestamp: '2024-01-01T00:00:00.000Z',
          requestId: 'uuid',
          error: {
            code: 'ERROR_CODE',
            message: 'Error message'
          }
        }
      }
    }, 'API文档获取成功');
  });
}

// API v1 认证端点
app.post('/api/v1/auth/login', Validators.login, async (req, res) => {
  console.log('\n🔐 API v1 登录请求');
  
  try {
    const { email, password, username } = req.body;
    const loginField = email || username;
    
    // 使用Parse SDK进行登录
    const Parse = require('parse/node');
    Parse.initialize(parseConfig.appId, parseConfig.javascriptKey, parseConfig.masterKey);
    Parse.serverURL = parseConfig.serverURL;
    
    let user;
    
    // 尝试用邮箱登录
    if (email) {
      const query = new Parse.Query(Parse.User);
      query.equalTo('email', email);
      const userObj = await query.first({ useMasterKey: true });
      
      if (userObj) {
        user = await Parse.User.logIn(userObj.get('username'), password);
      } else {
        throw new Error('User not found');
      }
    } else {
      user = await Parse.User.logIn(username, password);
    }
    
    // 更新最后登录时间
    user.set('lastLoginAt', new Date());
    await user.save(null, { useMasterKey: true });
    
    console.log('✅ API v1 登录成功:', user.get('username'));
    
    const userData = DataTransformers.userToStandard(user, true);
    
    res.success({
      user: userData,
      sessionToken: user.getSessionToken(),
      expiresAt: new Date(Date.now() + parseConfig.sessionLength * 1000).toISOString()
    }, '登录成功');
    
  } catch (error) {
    console.log('❌ API v1 登录失败:', error);

    let errorMessage = 'Unknown error';
    if (error && typeof error.message === 'string') {
      errorMessage = error.message;
    } else if (error && typeof error.toString === 'function') {
      errorMessage = error.toString();
    }

    if (errorMessage.includes('unauthorized') || errorMessage.includes('Invalid username/password')) {
      res.error('INVALID_CREDENTIALS', '用户名或密码错误');
    } else if (errorMessage.includes('User not found')) {
      res.error('INVALID_CREDENTIALS', '用户不存在');
    } else {
      res.internalError('登录失败', { originalError: errorMessage });
    }
  }
});

// API v1 用户信息端点
app.get('/api/v1/auth/me', Validators.authToken, async (req, res) => {
  console.log('\n👤 API v1 用户信息请求');
  
  try {
    const Parse = require('parse/node');
    Parse.initialize(parseConfig.appId, parseConfig.javascriptKey, parseConfig.masterKey);
    Parse.serverURL = parseConfig.serverURL;
    
    const user = await Parse.User.become(req.sessionToken);
    const userData = DataTransformers.userToStandard(user);
    
    res.success(userData, '获取用户信息成功');
    
  } catch (error) {
    console.log('❌ API v1 用户信息失败:', error.message);
    res.unauthorized('会话已过期，请重新登录');
  }
});

// 管理后台路由
const adminRoutes = require('./admin/routes/index');
app.use('/admin', adminRoutes);

// 根路径
app.get('/', (req, res) => {
  res.success({
    name: 'TaskFlow API Server',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    endpoints: {
      parse: '/parse',
      health: '/health',
      admin: '/admin',
      api: '/api/v1'
    },
    documentation: process.env.NODE_ENV === 'development' ? '/dev/models' : null,
    savedCredentials: process.env.NODE_ENV === 'development' ? '/dev/credentials' : null
  }, 'TaskFlow API服务器运行正常');
});

// 404处理
app.use(notFoundHandler);

// 错误处理
app.use(errorHandler);

// 启动服务器
async function startServer() {
  try {
    console.log('🔌 连接 MongoDB...');
    
    await mongoose.connect(parseConfig.databaseURI);
    console.log('✅ MongoDB 连接成功');
    
    console.log('🚀 启动 Parse Server...');
    const parseServer = new ParseServer(parseConfig);
    await parseServer.start();
    app.use(parseConfig.mountPath, parseServer.app);
    console.log('✅ Parse Server 启动成功');
    
    app.listen(PORT, '0.0.0.0', () => {
      console.log('🚀 TaskFlow 统一服务器启动成功!');
      console.log(`📍 服务器地址: http://localhost:${PORT}`);
      console.log(`🔗 Parse Server: http://localhost:${PORT}${parseConfig.mountPath}`);
      console.log(`🔗 API v1: http://localhost:${PORT}/api/v1`);
      console.log(`🔗 网络地址: http://************:${PORT}`);
      console.log(`💚 健康检查: http://localhost:${PORT}/health`);
      console.log(`🔧 管理后台: http://localhost:${PORT}/admin`);
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`🛠️  开发工具: http://localhost:${PORT}/dev/models`);
        console.log(`🔑 保存的凭据: http://localhost:${PORT}/dev/credentials`);
      }
      
      console.log('');
      console.log('📊 测试用户凭据:');
      console.log('  邮箱: <EMAIL>');
      console.log('  密码: Test123!');
      console.log('  用户名: demo_user');
      console.log('');
      console.log('🧪 测试API v1登录:');
      console.log(`  curl -X POST http://localhost:${PORT}/api/v1/auth/login \\`);
      console.log(`    -H "Content-Type: application/json" \\`);
      console.log(`    -d '{"email":"<EMAIL>","password":"Test123!"}'`);
    });
    
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 正在关闭服务器...');
  
  try {
    await mongoose.disconnect();
    console.log('✅ MongoDB 连接已关闭');
    process.exit(0);
  } catch (error) {
    console.error('❌ 关闭过程中出错:', error);
    process.exit(1);
  }
});

startServer();

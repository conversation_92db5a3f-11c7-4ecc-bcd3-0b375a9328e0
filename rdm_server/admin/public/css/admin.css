/* TaskFlow 管理后台样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fc;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
}

.nav-link {
    font-weight: 500;
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 0.375rem;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 600;
}

/* 统计卡片样式 */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

/* 头像样式 */
.avatar-sm {
    width: 2.5rem;
    height: 2.5rem;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
    background-color: #f8f9fc;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    border-radius: 0.375rem;
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 0.375rem;
    margin: 0 0.125rem;
    border: 1px solid #e3e6f0;
    color: #5a5c69;
}

.pagination .page-item.active .page-link {
    background-color: #4e73df;
    border-color: #4e73df;
}

/* 模态框样式 */
.modal-content {
    border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    border-radius: 0.5rem;
}

.modal-header {
    border-bottom: 1px solid #e3e6f0;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
}

/* Toast 样式 */
.toast {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* 图表容器样式 */
.chart-area {
    position: relative;
    height: 20rem;
}

.chart-pie {
    position: relative;
    height: 15rem;
}

/* 加载动画样式 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 搜索框样式 */
.input-group-text {
    background-color: #f8f9fc;
    border-color: #e3e6f0;
    color: #5a5c69;
}

/* 下拉菜单样式 */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
}

.dropdown-item {
    font-weight: 500;
}

.dropdown-item:hover {
    background-color: #f8f9fc;
}

/* 侧边栏样式 */
.sidebar {
    background-color: #ffffff;
    border-right: 1px solid #e3e6f0;
}

/* 快速统计样式 */
#quickStats .stat-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e3e6f0;
}

#quickStats .stat-item:last-child {
    border-bottom: none;
}

#quickStats .stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: #4e73df;
}

#quickStats .stat-label {
    font-size: 0.875rem;
    color: #5a5c69;
}

/* 状态指示器 */
.status-indicator {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-online {
    background-color: #1cc88a;
}

.status-offline {
    background-color: #e74a3b;
}

.status-away {
    background-color: #f6c23e;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group-sm > .btn {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }
    
    .chart-area,
    .chart-pie {
        height: 15rem;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 表单样式 */
.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 0.5rem;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 0.25rem;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 0.25rem;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 数据表格特殊样式 */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin-top: 1rem;
}

.dataTables_wrapper .dataTables_filter input {
    border-radius: 0.375rem;
    border: 1px solid #e3e6f0;
}

/* 错误页面样式 */
.error-page {
    text-align: center;
    padding: 3rem 0;
}

.error-code {
    font-size: 6rem;
    font-weight: 700;
    color: #e74a3b;
}

.error-message {
    font-size: 1.5rem;
    color: #5a5c69;
    margin-bottom: 2rem;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 加载状态样式 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* 打印样式 */
@media print {
    .navbar,
    .sidebar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}

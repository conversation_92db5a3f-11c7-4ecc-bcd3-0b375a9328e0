/**
 * TaskFlow 管理后台 JavaScript
 */

// 全局变量
window.adminApp = {
    currentUser: null,
    stats: null,
    charts: {}
};

// 页面加载完成后执行
$(document).ready(function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    // 初始化工具提示
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // 初始化弹出框
    $('[data-bs-toggle="popover"]').popover();
    
    // 设置AJAX默认配置
    $.ajaxSetup({
        beforeSend: function(xhr) {
            // 可以在这里添加认证头
        },
        error: function(xhr, status, error) {
            if (xhr.status === 401) {
                window.location.href = '/admin';
            }
        }
    });
}

// 加载快速统计
function loadQuickStats() {
    $.get('/admin/api/stats', function(data) {
        window.adminApp.stats = data;
        renderQuickStats(data);
    }).fail(function() {
        $('#quickStats').html('<div class="text-danger">加载失败</div>');
    });
}

// 渲染快速统计
function renderQuickStats(stats) {
    const html = `
        <div class="stat-item">
            <div class="stat-value">${stats.users.total}</div>
            <div class="stat-label">总用户数</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">${stats.tasks.total}</div>
            <div class="stat-label">总任务数</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">${stats.habits.total}</div>
            <div class="stat-label">总习惯数</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">${stats.habitRecords.total}</div>
            <div class="stat-label">习惯记录</div>
        </div>
    `;
    $('#quickStats').html(html);
}

// 刷新数据
function refreshData() {
    showToast('正在刷新数据...');
    loadQuickStats();
    
    // 如果当前页面有特定的刷新函数，调用它
    if (typeof refreshCurrentPage === 'function') {
        refreshCurrentPage();
    }
}

// 生成测试数据
function generateTestData() {
    showConfirm('确定要生成测试数据吗？', function() {
        showToast('正在生成测试数据，请稍候...');
        
        // 这里可以调用后端API生成测试数据
        setTimeout(function() {
            showToast('测试数据生成完成');
            refreshData();
        }, 2000);
    });
}

// 清理测试数据
function cleanTestData() {
    showConfirm('确定要清理所有测试数据吗？此操作不可恢复！', function() {
        showToast('正在清理测试数据，请稍候...');
        
        // 这里可以调用后端API清理测试数据
        setTimeout(function() {
            showToast('测试数据清理完成');
            refreshData();
        }, 2000);
    });
}

// 更新服务器时间
function updateServerTime() {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    $('#serverTime').text(timeString);
}

// 显示Toast通知
function showToast(message, type = 'info') {
    const toast = $('#toast');
    const toastMessage = $('#toastMessage');
    
    // 设置消息
    toastMessage.text(message);
    
    // 设置图标和颜色
    const iconClass = {
        'info': 'fas fa-info-circle text-primary',
        'success': 'fas fa-check-circle text-success',
        'warning': 'fas fa-exclamation-triangle text-warning',
        'error': 'fas fa-times-circle text-danger'
    }[type] || 'fas fa-info-circle text-primary';
    
    toast.find('.toast-header i').attr('class', iconClass);
    
    // 显示Toast
    const bsToast = new bootstrap.Toast(toast[0]);
    bsToast.show();
}

// 显示确认对话框
function showConfirm(message, callback) {
    $('#confirmMessage').text(message);
    $('#confirmButton').off('click').on('click', function() {
        $('#confirmModal').modal('hide');
        if (callback) callback();
    });
    $('#confirmModal').modal('show');
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 格式化相对时间
function formatRelativeTime(dateString) {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
        return `${days}天前`;
    } else if (hours > 0) {
        return `${hours}小时前`;
    } else if (minutes > 0) {
        return `${minutes}分钟前`;
    } else {
        return '刚刚';
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(function() {
            showToast('已复制到剪贴板');
        });
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast('已复制到剪贴板');
    }
}

// 导出数据为CSV
function exportToCSV(data, filename) {
    const csv = convertToCSV(data);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// 转换数据为CSV格式
function convertToCSV(data) {
    if (!data || data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => {
            const value = row[header];
            return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
        }).join(','))
    ].join('\n');
    
    return csvContent;
}

// 登出
function logout() {
    showConfirm('确定要退出登录吗？', function() {
        $.post('/admin/logout', function() {
            window.location.href = '/admin';
        });
    });
}

// 全屏切换
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        }
    }
}

// 打印页面
function printPage() {
    window.print();
}

// 搜索高亮
function highlightSearchTerm(text, term) {
    if (!term) return text;
    
    const regex = new RegExp(`(${term})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

// 防抖函数
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 获取URL参数
function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

// 设置URL参数
function setUrlParameter(key, value) {
    const url = new URL(window.location);
    url.searchParams.set(key, value);
    window.history.pushState({}, '', url);
}

// 移除URL参数
function removeUrlParameter(key) {
    const url = new URL(window.location);
    url.searchParams.delete(key);
    window.history.pushState({}, '', url);
}

// 检查网络状态
function checkNetworkStatus() {
    return navigator.onLine;
}

// 监听网络状态变化
window.addEventListener('online', function() {
    showToast('网络连接已恢复', 'success');
});

window.addEventListener('offline', function() {
    showToast('网络连接已断开', 'warning');
});

// 键盘快捷键
$(document).keydown(function(e) {
    // Ctrl+R 刷新数据
    if (e.ctrlKey && e.keyCode === 82) {
        e.preventDefault();
        refreshData();
    }
    
    // Esc 关闭模态框
    if (e.keyCode === 27) {
        $('.modal').modal('hide');
    }
});

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // 页面变为可见时，可以刷新数据
        if (typeof onPageVisible === 'function') {
            onPageVisible();
        }
    }
});

// 错误处理
window.addEventListener('error', function(e) {
    console.error('JavaScript错误:', e.error);
    showToast('页面发生错误，请刷新重试', 'error');
});

// 未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(e) {
    console.error('未处理的Promise拒绝:', e.reason);
    showToast('操作失败，请重试', 'error');
});

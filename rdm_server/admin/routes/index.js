/**
 * TaskFlow 管理后台主路由
 */

const express = require('express');
const Parse = require('parse/node');
const router = express.Router();

/**
 * 简单的认证中间件
 */
function requireAuth(req, res, next) {
  // 开发环境简单认证
  if (process.env.NODE_ENV === 'production') {
    return res.status(403).json({ error: '管理后台仅在开发环境可用' });
  }
  
  // 检查会话
  if (req.session && req.session.adminAuth) {
    return next();
  }
  
  // 检查基本认证
  const auth = req.headers.authorization;
  if (auth) {
    const credentials = Buffer.from(auth.split(' ')[1], 'base64').toString().split(':');
    const username = credentials[0];
    const password = credentials[1];
    
    if (username === 'admin' && password === (process.env.ADMIN_PASSWORD || 'admin123')) {
      req.session.adminAuth = true;
      return next();
    }
  }
  
  res.set('WWW-Authenticate', 'Basic realm="TaskFlow Admin"');
  res.status(401).send('需要认证');
}

/**
 * 获取数据统计
 */
async function getDataStats() {
  try {
    const [userCount, taskCount, habitCount, habitRecordCount] = await Promise.all([
      new Parse.Query(Parse.User).count({ useMasterKey: true }),
      new Parse.Query('Task').count({ useMasterKey: true }),
      new Parse.Query('Habit').count({ useMasterKey: true }),
      new Parse.Query('HabitRecord').count({ useMasterKey: true })
    ]);
    
    // 获取最近7天的用户注册数
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const recentUsers = await new Parse.Query(Parse.User)
      .greaterThan('createdAt', sevenDaysAgo)
      .count({ useMasterKey: true });
    
    // 获取今日完成的任务数
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todayCompletedTasks = await new Parse.Query('Task')
      .equalTo('isCompleted', true)
      .greaterThan('updatedAt', today)
      .count({ useMasterKey: true });
    
    return {
      users: {
        total: userCount,
        recent: recentUsers
      },
      tasks: {
        total: taskCount,
        completedToday: todayCompletedTasks
      },
      habits: {
        total: habitCount
      },
      habitRecords: {
        total: habitRecordCount
      }
    };
  } catch (error) {
    console.error('获取统计数据失败:', error);
    return null;
  }
}

/**
 * 管理后台首页
 */
router.get('/', requireAuth, async (req, res) => {
  try {
    const stats = await getDataStats();
    res.render('admin/dashboard', {
      title: 'TaskFlow 管理后台',
      stats: stats,
      currentPage: 'dashboard'
    });
  } catch (error) {
    res.status(500).render('admin/error', { error: error.message });
  }
});

/**
 * API: 获取统计数据
 */
router.get('/api/stats', requireAuth, async (req, res) => {
  try {
    const stats = await getDataStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * API: 获取用户列表
 */
router.get('/api/users', requireAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search || '';
    
    const query = new Parse.Query(Parse.User);
    
    if (search) {
      const emailQuery = new Parse.Query(Parse.User);
      emailQuery.contains('email', search);
      
      const usernameQuery = new Parse.Query(Parse.User);
      usernameQuery.contains('username', search);
      
      query._orQuery([emailQuery, usernameQuery]);
    }
    
    query.limit(limit);
    query.skip((page - 1) * limit);
    query.descending('createdAt');
    
    const users = await query.find({ useMasterKey: true });
    const total = await query.count({ useMasterKey: true });
    
    const userData = users.map(user => ({
      id: user.id,
      username: user.get('username'),
      email: user.get('email'),
      fullName: user.get('fullName'),
      isAdmin: user.get('isAdmin'),
      isActive: user.get('isActive'),
      isEmailVerified: user.get('isEmailVerified'),
      createdAt: user.get('createdAt'),
      updatedAt: user.get('updatedAt'),
      lastLoginAt: user.get('lastLoginAt')
    }));
    
    res.json({
      users: userData,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * API: 获取任务列表
 */
router.get('/api/tasks', requireAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const userId = req.query.userId;
    const status = req.query.status;
    const priority = req.query.priority;
    
    const query = new Parse.Query('Task');
    query.include('user');
    
    if (userId) {
      query.equalTo('userId', userId);
    }
    
    if (status) {
      query.equalTo('status', status);
    }
    
    if (priority) {
      query.equalTo('priority', priority);
    }
    
    query.limit(limit);
    query.skip((page - 1) * limit);
    query.descending('createdAt');
    
    const tasks = await query.find({ useMasterKey: true });
    const total = await query.count({ useMasterKey: true });
    
    const taskData = tasks.map(task => ({
      id: task.id,
      title: task.get('title'),
      description: task.get('description'),
      priority: task.get('priority'),
      status: task.get('status'),
      category: task.get('category'),
      isCompleted: task.get('isCompleted'),
      dueDate: task.get('dueDate'),
      createdAt: task.get('createdAt'),
      updatedAt: task.get('updatedAt'),
      user: {
        id: task.get('user')?.id,
        username: task.get('user')?.get('username'),
        email: task.get('user')?.get('email')
      }
    }));
    
    res.json({
      tasks: taskData,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * API: 获取习惯列表
 */
router.get('/api/habits', requireAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const userId = req.query.userId;
    const type = req.query.type;
    
    const query = new Parse.Query('Habit');
    query.include('user');
    
    if (userId) {
      query.equalTo('userId', userId);
    }
    
    if (type) {
      query.equalTo('type', type);
    }
    
    query.limit(limit);
    query.skip((page - 1) * limit);
    query.descending('createdAt');
    
    const habits = await query.find({ useMasterKey: true });
    const total = await query.count({ useMasterKey: true });
    
    const habitData = habits.map(habit => ({
      id: habit.id,
      name: habit.get('name'),
      description: habit.get('description'),
      type: habit.get('type'),
      frequency: habit.get('frequency'),
      targetValue: habit.get('targetValue'),
      unit: habit.get('unit'),
      isActive: habit.get('isActive'),
      category: habit.get('category'),
      createdAt: habit.get('createdAt'),
      updatedAt: habit.get('updatedAt'),
      user: {
        id: habit.get('user')?.id,
        username: habit.get('user')?.get('username'),
        email: habit.get('user')?.get('email')
      }
    }));
    
    res.json({
      habits: habitData,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * API: 删除用户
 */
router.delete('/api/users/:id', requireAuth, async (req, res) => {
  try {
    const userId = req.params.id;
    
    // 保护管理员账户
    const user = await new Parse.Query(Parse.User).get(userId, { useMasterKey: true });
    if (user.get('email') === '<EMAIL>') {
      return res.status(403).json({ error: '不能删除管理员账户' });
    }
    
    await user.destroy({ useMasterKey: true });
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * 用户管理页面
 */
router.get('/users', requireAuth, (req, res) => {
  res.render('admin/users', {
    title: '用户管理',
    currentPage: 'users'
  });
});

/**
 * 任务管理页面
 */
router.get('/tasks', requireAuth, (req, res) => {
  res.render('admin/tasks', {
    title: '任务管理',
    currentPage: 'tasks'
  });
});

/**
 * 习惯管理页面
 */
router.get('/habits', requireAuth, (req, res) => {
  res.render('admin/habits', {
    title: '习惯管理',
    currentPage: 'habits'
  });
});

/**
 * 登出
 */
router.post('/logout', (req, res) => {
  req.session.destroy();
  res.json({ success: true });
});

module.exports = router;

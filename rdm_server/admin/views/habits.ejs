<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">
        <i class="fas fa-calendar-check me-2"></i>
        习惯管理
    </h1>
    <div>
        <button class="btn btn-outline-secondary me-2" onclick="exportHabits()">
            <i class="fas fa-download me-1"></i>
            导出
        </button>
        <button class="btn btn-primary" onclick="refreshHabits()">
            <i class="fas fa-sync-alt me-1"></i>
            刷新
        </button>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索习惯名称...">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="typeFilter">
                    <option value="">所有类型</option>
                    <option value="boolean">布尔类型</option>
                    <option value="numeric">数值类型</option>
                    <option value="duration">时长类型</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="frequencyFilter">
                    <option value="">所有频率</option>
                    <option value="daily">每日</option>
                    <option value="weekly">每周</option>
                    <option value="monthly">每月</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="userFilter">
                    <option value="">所有用户</option>
                    <!-- 用户选项将通过JavaScript填充 -->
                </select>
            </div>
            <div class="col-md-3">
                <div class="btn-group w-100">
                    <button class="btn btn-outline-primary" onclick="applyFilters()">
                        <i class="fas fa-filter me-1"></i>
                        筛选
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i>
                        清除
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 习惯列表 -->
<div class="card">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-list me-1"></i>
            习惯列表
            <span class="badge bg-primary ms-2" id="habitCount">0</span>
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="habitsTable">
                <thead>
                    <tr>
                        <th>图标</th>
                        <th>名称</th>
                        <th>用户</th>
                        <th>类型</th>
                        <th>频率</th>
                        <th>目标值</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="habitsTableBody">
                    <tr>
                        <td colspan="9" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <nav aria-label="习惯列表分页">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将通过JavaScript生成 -->
            </ul>
        </nav>
    </div>
</div>

<!-- 习惯详情模态框 -->
<div class="modal fade" id="habitDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-calendar-check me-2"></i>
                    习惯详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="habitDetailContent">
                <!-- 习惯详情内容将通过JavaScript填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let currentFilters = {};
let habitsData = [];
let usersData = [];

// 页面加载完成后执行
$(document).ready(function() {
    loadUsers();
    loadHabits();
    
    // 搜索框事件
    $('#searchInput').on('keyup', debounce(function() {
        applyFilters();
    }, 500));
});

// 加载用户列表（用于筛选）
function loadUsers() {
    $.get('/admin/api/users?limit=100', function(data) {
        usersData = data.users;
        renderUserFilter(data.users);
    });
}

// 渲染用户筛选器
function renderUserFilter(users) {
    let html = '<option value="">所有用户</option>';
    users.forEach(user => {
        html += `<option value="${user.id}">${user.fullName || user.username}</option>`;
    });
    $('#userFilter').html(html);
}

// 加载习惯列表
function loadHabits(page = 1) {
    currentPage = page;
    
    const params = new URLSearchParams({
        page: page,
        limit: 20,
        ...currentFilters
    });
    
    $.get(`/admin/api/habits?${params}`, function(data) {
        habitsData = data.habits;
        renderHabitsTable(data.habits);
        renderPagination(data.pagination);
        $('#habitCount').text(data.pagination.total);
    }).fail(function() {
        showToast('加载习惯列表失败', 'error');
    });
}

// 渲染习惯表格
function renderHabitsTable(habits) {
    let html = '';
    
    if (habits.length === 0) {
        html = '<tr><td colspan="9" class="text-center text-muted">暂无数据</td></tr>';
    } else {
        habits.forEach(habit => {
            const typeClass = {
                'boolean': 'primary',
                'numeric': 'success',
                'duration': 'info'
            }[habit.type] || 'secondary';
            
            const typeName = {
                'boolean': '布尔类型',
                'numeric': '数值类型',
                'duration': '时长类型'
            }[habit.type] || habit.type;
            
            const frequencyName = {
                'daily': '每日',
                'weekly': '每周',
                'monthly': '每月'
            }[habit.frequency] || habit.frequency;
            
            const statusClass = habit.isActive ? 'success' : 'secondary';
            const statusName = habit.isActive ? '活跃' : '非活跃';
            
            html += `
                <tr>
                    <td>
                        <div class="text-center" style="font-size: 1.5rem;">
                            ${habit.icon || '🎯'}
                        </div>
                    </td>
                    <td>
                        <div>
                            <div class="fw-bold">${habit.name}</div>
                            <small class="text-muted">${habit.description ? habit.description.substring(0, 50) + '...' : ''}</small>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm me-2">
                                <div class="avatar-title bg-primary rounded-circle">
                                    ${habit.user.username ? habit.user.username.charAt(0).toUpperCase() : 'U'}
                                </div>
                            </div>
                            <div>
                                <div class="fw-bold">${habit.user.username || '-'}</div>
                                <small class="text-muted">${habit.user.email || ''}</small>
                            </div>
                        </div>
                    </td>
                    <td><span class="badge bg-${typeClass}">${typeName}</span></td>
                    <td>${frequencyName}</td>
                    <td>
                        ${habit.targetValue} ${habit.unit || ''}
                    </td>
                    <td><span class="badge bg-${statusClass}">${statusName}</span></td>
                    <td>${formatDate(habit.createdAt)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewHabit('${habit.id}')" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
    }
    
    $('#habitsTableBody').html(html);
}

// 渲染分页
function renderPagination(pagination) {
    let html = '';
    
    // 上一页
    html += `
        <li class="page-item ${pagination.page <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadHabits(${pagination.page - 1})">上一页</a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.pages, pagination.page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === pagination.page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadHabits(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    html += `
        <li class="page-item ${pagination.page >= pagination.pages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadHabits(${pagination.page + 1})">下一页</a>
        </li>
    `;
    
    $('#pagination').html(html);
}

// 应用筛选
function applyFilters() {
    currentFilters = {
        search: $('#searchInput').val(),
        type: $('#typeFilter').val(),
        frequency: $('#frequencyFilter').val(),
        userId: $('#userFilter').val()
    };
    
    // 移除空值
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
            delete currentFilters[key];
        }
    });
    
    loadHabits(1);
}

// 清除筛选
function clearFilters() {
    $('#searchInput').val('');
    $('#typeFilter').val('');
    $('#frequencyFilter').val('');
    $('#userFilter').val('');
    currentFilters = {};
    loadHabits(1);
}

// 查看习惯详情
function viewHabit(habitId) {
    const habit = habitsData.find(h => h.id === habitId);
    if (!habit) return;
    
    const html = `
        <div class="row">
            <div class="col-md-8">
                <h6>习惯信息</h6>
                <table class="table table-sm">
                    <tr><td>习惯ID:</td><td>${habit.id}</td></tr>
                    <tr><td>名称:</td><td>${habit.name}</td></tr>
                    <tr><td>描述:</td><td>${habit.description || '-'}</td></tr>
                    <tr><td>类型:</td><td><span class="badge bg-${getTypeClass(habit.type)}">${getTypeName(habit.type)}</span></td></tr>
                    <tr><td>频率:</td><td>${getFrequencyName(habit.frequency)}</td></tr>
                    <tr><td>目标值:</td><td>${habit.targetValue} ${habit.unit || ''}</td></tr>
                    <tr><td>分类:</td><td>${habit.category || '-'}</td></tr>
                    <tr><td>图标:</td><td style="font-size: 1.5rem;">${habit.icon || '🎯'}</td></tr>
                    <tr><td>颜色:</td><td><span class="badge" style="background-color: ${habit.color || '#4A90E2'}">${habit.color || '#4A90E2'}</span></td></tr>
                    <tr><td>状态:</td><td><span class="badge bg-${habit.isActive ? 'success' : 'secondary'}">${habit.isActive ? '活跃' : '非活跃'}</span></td></tr>
                </table>
            </div>
            <div class="col-md-4">
                <h6>时间信息</h6>
                <table class="table table-sm">
                    <tr><td>创建时间:</td><td>${formatDate(habit.createdAt)}</td></tr>
                    <tr><td>更新时间:</td><td>${formatDate(habit.updatedAt)}</td></tr>
                </table>
                
                <h6>用户信息</h6>
                <table class="table table-sm">
                    <tr><td>用户名:</td><td>${habit.user.username || '-'}</td></tr>
                    <tr><td>邮箱:</td><td>${habit.user.email || '-'}</td></tr>
                </table>
            </div>
        </div>
    `;
    
    $('#habitDetailContent').html(html);
    $('#habitDetailModal').modal('show');
}

// 获取类型样式类
function getTypeClass(type) {
    return {
        'boolean': 'primary',
        'numeric': 'success',
        'duration': 'info'
    }[type] || 'secondary';
}

// 获取类型名称
function getTypeName(type) {
    return {
        'boolean': '布尔类型',
        'numeric': '数值类型',
        'duration': '时长类型'
    }[type] || type;
}

// 获取频率名称
function getFrequencyName(frequency) {
    return {
        'daily': '每日',
        'weekly': '每周',
        'monthly': '每月'
    }[frequency] || frequency;
}

// 刷新习惯列表
function refreshHabits() {
    loadHabits(currentPage);
}

// 导出习惯数据
function exportHabits() {
    showToast('导出功能开发中...');
}

// 设置当前页面为习惯管理
window.currentPage = 'habits';
</script>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">
        <i class="fas fa-users me-2"></i>
        用户管理
    </h1>
    <div>
        <button class="btn btn-outline-secondary me-2" onclick="exportUsers()">
            <i class="fas fa-download me-1"></i>
            导出
        </button>
        <button class="btn btn-primary" onclick="refreshUsers()">
            <i class="fas fa-sync-alt me-1"></i>
            刷新
        </button>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索用户名或邮箱...">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="roleFilter">
                    <option value="">所有角色</option>
                    <option value="admin">管理员</option>
                    <option value="user">普通用户</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="statusFilter">
                    <option value="">所有状态</option>
                    <option value="active">活跃</option>
                    <option value="inactive">非活跃</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-primary w-100" onclick="applyFilters()">
                    <i class="fas fa-filter me-1"></i>
                    筛选
                </button>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times me-1"></i>
                    清除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 用户列表 -->
<div class="card">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-list me-1"></i>
            用户列表
            <span class="badge bg-primary ms-2" id="userCount">0</span>
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="usersTable">
                <thead>
                    <tr>
                        <th>头像</th>
                        <th>用户名</th>
                        <th>邮箱</th>
                        <th>姓名</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th>注册时间</th>
                        <th>最后登录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    <tr>
                        <td colspan="9" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <nav aria-label="用户列表分页">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将通过JavaScript生成 -->
            </ul>
        </nav>
    </div>
</div>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user me-2"></i>
                    用户详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailContent">
                <!-- 用户详情内容将通过JavaScript填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="editUser()">编辑用户</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let currentFilters = {};
let usersData = [];

// 页面加载完成后执行
$(document).ready(function() {
    loadUsers();
    
    // 搜索框事件
    $('#searchInput').on('keyup', debounce(function() {
        applyFilters();
    }, 500));
});

// 加载用户列表
function loadUsers(page = 1) {
    currentPage = page;
    
    const params = new URLSearchParams({
        page: page,
        limit: 20,
        ...currentFilters
    });
    
    $.get(`/admin/api/users?${params}`, function(data) {
        usersData = data.users;
        renderUsersTable(data.users);
        renderPagination(data.pagination);
        $('#userCount').text(data.pagination.total);
    }).fail(function() {
        showToast('加载用户列表失败', 'error');
    });
}

// 渲染用户表格
function renderUsersTable(users) {
    let html = '';
    
    if (users.length === 0) {
        html = '<tr><td colspan="9" class="text-center text-muted">暂无数据</td></tr>';
    } else {
        users.forEach(user => {
            const avatar = user.username.charAt(0).toUpperCase();
            const roleClass = user.isAdmin ? 'danger' : 'primary';
            const roleName = user.isAdmin ? '管理员' : '普通用户';
            const statusClass = user.isActive ? 'success' : 'secondary';
            const statusName = user.isActive ? '活跃' : '非活跃';
            const verifiedIcon = user.isEmailVerified ? 
                '<i class="fas fa-check-circle text-success" title="邮箱已验证"></i>' : 
                '<i class="fas fa-times-circle text-danger" title="邮箱未验证"></i>';
            
            html += `
                <tr>
                    <td>
                        <div class="avatar-sm">
                            <div class="avatar-title bg-primary rounded-circle">
                                ${avatar}
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <span class="me-2">${user.username}</span>
                            ${verifiedIcon}
                        </div>
                    </td>
                    <td>${user.email}</td>
                    <td>${user.fullName || '-'}</td>
                    <td><span class="badge bg-${roleClass}">${roleName}</span></td>
                    <td><span class="badge bg-${statusClass}">${statusName}</span></td>
                    <td>${formatDate(user.createdAt)}</td>
                    <td>${user.lastLoginAt ? formatDate(user.lastLoginAt) : '-'}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewUser('${user.id}')" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="viewUserTasks('${user.id}')" title="查看任务">
                                <i class="fas fa-tasks"></i>
                            </button>
                            ${!user.isAdmin ? `
                                <button class="btn btn-outline-danger" onclick="deleteUser('${user.id}', '${user.username}')" title="删除用户">
                                    <i class="fas fa-trash"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `;
        });
    }
    
    $('#usersTableBody').html(html);
}

// 渲染分页
function renderPagination(pagination) {
    let html = '';
    
    // 上一页
    html += `
        <li class="page-item ${pagination.page <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadUsers(${pagination.page - 1})">上一页</a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.pages, pagination.page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === pagination.page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadUsers(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    html += `
        <li class="page-item ${pagination.page >= pagination.pages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadUsers(${pagination.page + 1})">下一页</a>
        </li>
    `;
    
    $('#pagination').html(html);
}

// 应用筛选
function applyFilters() {
    currentFilters = {
        search: $('#searchInput').val(),
        role: $('#roleFilter').val(),
        status: $('#statusFilter').val()
    };
    
    // 移除空值
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
            delete currentFilters[key];
        }
    });
    
    loadUsers(1);
}

// 清除筛选
function clearFilters() {
    $('#searchInput').val('');
    $('#roleFilter').val('');
    $('#statusFilter').val('');
    currentFilters = {};
    loadUsers(1);
}

// 查看用户详情
function viewUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    const html = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>用户ID:</td><td>${user.id}</td></tr>
                    <tr><td>用户名:</td><td>${user.username}</td></tr>
                    <tr><td>邮箱:</td><td>${user.email}</td></tr>
                    <tr><td>姓名:</td><td>${user.fullName || '-'}</td></tr>
                    <tr><td>角色:</td><td>${user.isAdmin ? '管理员' : '普通用户'}</td></tr>
                    <tr><td>状态:</td><td>${user.isActive ? '活跃' : '非活跃'}</td></tr>
                    <tr><td>邮箱验证:</td><td>${user.isEmailVerified ? '已验证' : '未验证'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>时间信息</h6>
                <table class="table table-sm">
                    <tr><td>注册时间:</td><td>${formatDate(user.createdAt)}</td></tr>
                    <tr><td>更新时间:</td><td>${formatDate(user.updatedAt)}</td></tr>
                    <tr><td>最后登录:</td><td>${user.lastLoginAt ? formatDate(user.lastLoginAt) : '-'}</td></tr>
                </table>
            </div>
        </div>
    `;
    
    $('#userDetailContent').html(html);
    $('#userDetailModal').modal('show');
}

// 查看用户任务
function viewUserTasks(userId) {
    window.open(`/admin/tasks?userId=${userId}`, '_blank');
}

// 删除用户
function deleteUser(userId, username) {
    showConfirm(`确定要删除用户 "${username}" 吗？此操作不可恢复。`, function() {
        $.ajax({
            url: `/admin/api/users/${userId}`,
            method: 'DELETE',
            success: function() {
                showToast('用户删除成功');
                loadUsers(currentPage);
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.error || '删除失败';
                showToast(error, 'error');
            }
        });
    });
}

// 刷新用户列表
function refreshUsers() {
    loadUsers(currentPage);
}

// 导出用户数据
function exportUsers() {
    // 这里可以实现导出功能
    showToast('导出功能开发中...');
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">
        <i class="fas fa-tachometer-alt me-2"></i>
        数据概览
    </h1>
    <div>
        <button class="btn btn-outline-primary" onclick="refreshDashboard()">
            <i class="fas fa-sync-alt me-1"></i>
            刷新
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            用户总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalUsers">
                            <%= stats ? stats.users.total : 0 %>
                        </div>
                        <div class="text-xs text-muted">
                            最近7天新增: <span id="recentUsers"><%= stats ? stats.users.recent : 0 %></span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            任务总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalTasks">
                            <%= stats ? stats.tasks.total : 0 %>
                        </div>
                        <div class="text-xs text-muted">
                            今日完成: <span id="todayCompletedTasks"><%= stats ? stats.tasks.completedToday : 0 %></span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-list-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            习惯总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalHabits">
                            <%= stats ? stats.habits.total : 0 %>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            习惯记录
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalHabitRecords">
                            <%= stats ? stats.habitRecords.total : 0 %>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <!-- 用户增长趋势 -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-area me-1"></i>
                    数据趋势
                </h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="#" onclick="exportChart()">导出图表</a>
                        <a class="dropdown-item" href="#" onclick="refreshChart()">刷新数据</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="trendChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据分布 -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie me-1"></i>
                    数据分布
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="distributionChart" width="400" height="400"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-user-plus me-1"></i>
                    最新用户
                </h6>
            </div>
            <div class="card-body">
                <div id="recentUsersList">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-tasks me-1"></i>
                    最新任务
                </h6>
            </div>
            <div class="card-body">
                <div id="recentTasksList">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-server me-1"></i>
                    系统信息
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>服务器状态:</strong>
                        <span class="badge bg-success">运行中</span>
                    </div>
                    <div class="col-md-3">
                        <strong>数据库:</strong>
                        <span class="badge bg-success">已连接</span>
                    </div>
                    <div class="col-md-3">
                        <strong>环境:</strong>
                        <span class="badge bg-warning">开发环境</span>
                    </div>
                    <div class="col-md-3">
                        <strong>版本:</strong>
                        <span class="badge bg-info">v1.0.0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面特定的JavaScript
$(document).ready(function() {
    initCharts();
    loadRecentData();
});

// 初始化图表
function initCharts() {
    // 趋势图表
    const trendCtx = document.getElementById('trendChart').getContext('2d');
    window.trendChart = new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: ['7天前', '6天前', '5天前', '4天前', '3天前', '2天前', '昨天', '今天'],
            datasets: [{
                label: '用户注册',
                data: [12, 19, 3, 5, 2, 3, 8, 15],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1
            }, {
                label: '任务创建',
                data: [25, 35, 15, 28, 12, 18, 32, 45],
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // 分布图表
    const distributionCtx = document.getElementById('distributionChart').getContext('2d');
    window.distributionChart = new Chart(distributionCtx, {
        type: 'doughnut',
        data: {
            labels: ['用户', '任务', '习惯', '记录'],
            datasets: [{
                data: [
                    <%= stats ? stats.users.total : 0 %>,
                    <%= stats ? stats.tasks.total : 0 %>,
                    <%= stats ? stats.habits.total : 0 %>,
                    <%= stats ? stats.habitRecords.total : 0 %>
                ],
                backgroundColor: [
                    '#4e73df',
                    '#1cc88a',
                    '#36b9cc',
                    '#f6c23e'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// 加载最近数据
function loadRecentData() {
    // 加载最新用户
    $.get('/admin/api/users?limit=5', function(data) {
        let html = '';
        data.users.forEach(user => {
            html += `
                <div class="d-flex align-items-center mb-2">
                    <div class="avatar-sm me-3">
                        <div class="avatar-title bg-primary rounded-circle">
                            ${user.username.charAt(0).toUpperCase()}
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-0">${user.fullName || user.username}</h6>
                        <small class="text-muted">${user.email}</small>
                    </div>
                    <small class="text-muted">${formatDate(user.createdAt)}</small>
                </div>
            `;
        });
        $('#recentUsersList').html(html);
    });

    // 加载最新任务
    $.get('/admin/api/tasks?limit=5', function(data) {
        let html = '';
        data.tasks.forEach(task => {
            const priorityClass = {
                'high': 'danger',
                'medium': 'warning',
                'low': 'success'
            }[task.priority] || 'secondary';
            
            html += `
                <div class="d-flex align-items-center mb-2">
                    <div class="me-3">
                        <span class="badge bg-${priorityClass}">${task.priority}</span>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-0">${task.title}</h6>
                        <small class="text-muted">by ${task.user.username}</small>
                    </div>
                    <small class="text-muted">${formatDate(task.createdAt)}</small>
                </div>
            `;
        });
        $('#recentTasksList').html(html);
    });
}

// 刷新仪表板
function refreshDashboard() {
    location.reload();
}
</script>

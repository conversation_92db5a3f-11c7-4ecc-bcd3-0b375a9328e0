<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">
        <i class="fas fa-list-check me-2"></i>
        任务管理
    </h1>
    <div>
        <button class="btn btn-outline-secondary me-2" onclick="exportTasks()">
            <i class="fas fa-download me-1"></i>
            导出
        </button>
        <button class="btn btn-primary" onclick="refreshTasks()">
            <i class="fas fa-sync-alt me-1"></i>
            刷新
        </button>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索任务标题...">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="statusFilter">
                    <option value="">所有状态</option>
                    <option value="pending">待处理</option>
                    <option value="in_progress">进行中</option>
                    <option value="completed">已完成</option>
                    <option value="cancelled">已取消</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="priorityFilter">
                    <option value="">所有优先级</option>
                    <option value="high">高</option>
                    <option value="medium">中</option>
                    <option value="low">低</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="userFilter">
                    <option value="">所有用户</option>
                    <!-- 用户选项将通过JavaScript填充 -->
                </select>
            </div>
            <div class="col-md-3">
                <div class="btn-group w-100">
                    <button class="btn btn-outline-primary" onclick="applyFilters()">
                        <i class="fas fa-filter me-1"></i>
                        筛选
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i>
                        清除
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务列表 -->
<div class="card">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-list me-1"></i>
            任务列表
            <span class="badge bg-primary ms-2" id="taskCount">0</span>
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="tasksTable">
                <thead>
                    <tr>
                        <th>标题</th>
                        <th>用户</th>
                        <th>优先级</th>
                        <th>状态</th>
                        <th>分类</th>
                        <th>截止日期</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="tasksTableBody">
                    <tr>
                        <td colspan="8" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <nav aria-label="任务列表分页">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将通过JavaScript生成 -->
            </ul>
        </nav>
    </div>
</div>

<!-- 任务详情模态框 -->
<div class="modal fade" id="taskDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-tasks me-2"></i>
                    任务详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="taskDetailContent">
                <!-- 任务详情内容将通过JavaScript填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let currentFilters = {};
let tasksData = [];
let usersData = [];

// 页面加载完成后执行
$(document).ready(function() {
    loadUsers();
    loadTasks();
    
    // 搜索框事件
    $('#searchInput').on('keyup', debounce(function() {
        applyFilters();
    }, 500));
});

// 加载用户列表（用于筛选）
function loadUsers() {
    $.get('/admin/api/users?limit=100', function(data) {
        usersData = data.users;
        renderUserFilter(data.users);
    });
}

// 渲染用户筛选器
function renderUserFilter(users) {
    let html = '<option value="">所有用户</option>';
    users.forEach(user => {
        html += `<option value="${user.id}">${user.fullName || user.username}</option>`;
    });
    $('#userFilter').html(html);
}

// 加载任务列表
function loadTasks(page = 1) {
    currentPage = page;
    
    const params = new URLSearchParams({
        page: page,
        limit: 20,
        ...currentFilters
    });
    
    $.get(`/admin/api/tasks?${params}`, function(data) {
        tasksData = data.tasks;
        renderTasksTable(data.tasks);
        renderPagination(data.pagination);
        $('#taskCount').text(data.pagination.total);
    }).fail(function() {
        showToast('加载任务列表失败', 'error');
    });
}

// 渲染任务表格
function renderTasksTable(tasks) {
    let html = '';
    
    if (tasks.length === 0) {
        html = '<tr><td colspan="8" class="text-center text-muted">暂无数据</td></tr>';
    } else {
        tasks.forEach(task => {
            const priorityClass = {
                'high': 'danger',
                'medium': 'warning',
                'low': 'success'
            }[task.priority] || 'secondary';
            
            const statusClass = {
                'pending': 'secondary',
                'in_progress': 'primary',
                'completed': 'success',
                'cancelled': 'danger'
            }[task.status] || 'secondary';
            
            const statusName = {
                'pending': '待处理',
                'in_progress': '进行中',
                'completed': '已完成',
                'cancelled': '已取消'
            }[task.status] || task.status;
            
            const priorityName = {
                'high': '高',
                'medium': '中',
                'low': '低'
            }[task.priority] || task.priority;
            
            html += `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="me-2">
                                ${task.isCompleted ? '<i class="fas fa-check-circle text-success"></i>' : '<i class="far fa-circle text-muted"></i>'}
                            </div>
                            <div>
                                <div class="fw-bold">${task.title}</div>
                                <small class="text-muted">${task.description ? task.description.substring(0, 50) + '...' : ''}</small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm me-2">
                                <div class="avatar-title bg-primary rounded-circle">
                                    ${task.user.username ? task.user.username.charAt(0).toUpperCase() : 'U'}
                                </div>
                            </div>
                            <div>
                                <div class="fw-bold">${task.user.username || '-'}</div>
                                <small class="text-muted">${task.user.email || ''}</small>
                            </div>
                        </div>
                    </td>
                    <td><span class="badge bg-${priorityClass}">${priorityName}</span></td>
                    <td><span class="badge bg-${statusClass}">${statusName}</span></td>
                    <td>${task.category || '-'}</td>
                    <td>${task.dueDate ? formatDate(task.dueDate) : '-'}</td>
                    <td>${formatDate(task.createdAt)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewTask('${task.id}')" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
    }
    
    $('#tasksTableBody').html(html);
}

// 渲染分页
function renderPagination(pagination) {
    let html = '';
    
    // 上一页
    html += `
        <li class="page-item ${pagination.page <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadTasks(${pagination.page - 1})">上一页</a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.pages, pagination.page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === pagination.page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadTasks(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    html += `
        <li class="page-item ${pagination.page >= pagination.pages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadTasks(${pagination.page + 1})">下一页</a>
        </li>
    `;
    
    $('#pagination').html(html);
}

// 应用筛选
function applyFilters() {
    currentFilters = {
        search: $('#searchInput').val(),
        status: $('#statusFilter').val(),
        priority: $('#priorityFilter').val(),
        userId: $('#userFilter').val()
    };
    
    // 移除空值
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
            delete currentFilters[key];
        }
    });
    
    loadTasks(1);
}

// 清除筛选
function clearFilters() {
    $('#searchInput').val('');
    $('#statusFilter').val('');
    $('#priorityFilter').val('');
    $('#userFilter').val('');
    currentFilters = {};
    loadTasks(1);
}

// 查看任务详情
function viewTask(taskId) {
    const task = tasksData.find(t => t.id === taskId);
    if (!task) return;
    
    const html = `
        <div class="row">
            <div class="col-md-8">
                <h6>任务信息</h6>
                <table class="table table-sm">
                    <tr><td>任务ID:</td><td>${task.id}</td></tr>
                    <tr><td>标题:</td><td>${task.title}</td></tr>
                    <tr><td>描述:</td><td>${task.description || '-'}</td></tr>
                    <tr><td>优先级:</td><td><span class="badge bg-${getPriorityClass(task.priority)}">${getPriorityName(task.priority)}</span></td></tr>
                    <tr><td>状态:</td><td><span class="badge bg-${getStatusClass(task.status)}">${getStatusName(task.status)}</span></td></tr>
                    <tr><td>分类:</td><td>${task.category || '-'}</td></tr>
                    <tr><td>标签:</td><td>${task.tags ? task.tags.join(', ') : '-'}</td></tr>
                    <tr><td>是否完成:</td><td>${task.isCompleted ? '是' : '否'}</td></tr>
                </table>
            </div>
            <div class="col-md-4">
                <h6>时间信息</h6>
                <table class="table table-sm">
                    <tr><td>创建时间:</td><td>${formatDate(task.createdAt)}</td></tr>
                    <tr><td>更新时间:</td><td>${formatDate(task.updatedAt)}</td></tr>
                    <tr><td>截止日期:</td><td>${task.dueDate ? formatDate(task.dueDate) : '-'}</td></tr>
                </table>
                
                <h6>用户信息</h6>
                <table class="table table-sm">
                    <tr><td>用户名:</td><td>${task.user.username || '-'}</td></tr>
                    <tr><td>邮箱:</td><td>${task.user.email || '-'}</td></tr>
                </table>
            </div>
        </div>
    `;
    
    $('#taskDetailContent').html(html);
    $('#taskDetailModal').modal('show');
}

// 获取优先级样式类
function getPriorityClass(priority) {
    return {
        'high': 'danger',
        'medium': 'warning',
        'low': 'success'
    }[priority] || 'secondary';
}

// 获取优先级名称
function getPriorityName(priority) {
    return {
        'high': '高',
        'medium': '中',
        'low': '低'
    }[priority] || priority;
}

// 获取状态样式类
function getStatusClass(status) {
    return {
        'pending': 'secondary',
        'in_progress': 'primary',
        'completed': 'success',
        'cancelled': 'danger'
    }[status] || 'secondary';
}

// 获取状态名称
function getStatusName(status) {
    return {
        'pending': '待处理',
        'in_progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
    }[status] || status;
}

// 刷新任务列表
function refreshTasks() {
    loadTasks(currentPage);
}

// 导出任务数据
function exportTasks() {
    showToast('导出功能开发中...');
}

// 设置当前页面为任务管理
window.currentPage = 'tasks';
</script>

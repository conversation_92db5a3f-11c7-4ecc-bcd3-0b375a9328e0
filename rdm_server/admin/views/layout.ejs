<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - TaskFlow 管理后台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义样式 -->
    <link href="/admin/css/admin.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">
                <i class="fas fa-tasks me-2"></i>
                TaskFlow 管理后台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === 'dashboard' ? 'active' : '' %>" href="/admin">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === 'users' ? 'active' : '' %>" href="/admin/users">
                            <i class="fas fa-users me-1"></i>
                            用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === 'tasks' ? 'active' : '' %>" href="/admin/tasks">
                            <i class="fas fa-list-check me-1"></i>
                            任务管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === 'habits' ? 'active' : '' %>" href="/admin/habits">
                            <i class="fas fa-calendar-check me-1"></i>
                            习惯管理
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>
                                查看前台
                            </a></li>
                            <li><a class="dropdown-item" href="/health" target="_blank">
                                <i class="fas fa-heartbeat me-1"></i>
                                系统状态
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 d-none d-md-block">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-chart-bar me-1"></i>
                            快速统计
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="quickStats">
                            <div class="d-flex justify-content-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-tools me-1"></i>
                            快速操作
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                刷新数据
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="generateTestData()">
                                <i class="fas fa-plus me-1"></i>
                                生成测试数据
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="cleanTestData()">
                                <i class="fas fa-trash me-1"></i>
                                清理测试数据
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区域 -->
            <div class="col-md-10">
                <%- body %>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <span class="text-muted">
                TaskFlow 管理后台 &copy; 2024 
                <span class="ms-3">
                    <i class="fas fa-server me-1"></i>
                    服务器时间: <span id="serverTime"></span>
                </span>
            </span>
        </div>
    </footer>

    <!-- 模态框 -->
    <div class="modal fade" id="confirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p id="confirmMessage">确定要执行此操作吗？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmButton">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-info-circle me-2 text-primary"></i>
                <strong class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                操作完成
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script src="/admin/js/admin.js"></script>
    
    <script>
        // 页面加载完成后执行
        $(document).ready(function() {
            loadQuickStats();
            updateServerTime();
            setInterval(updateServerTime, 1000);
        });
    </script>
</body>
</html>

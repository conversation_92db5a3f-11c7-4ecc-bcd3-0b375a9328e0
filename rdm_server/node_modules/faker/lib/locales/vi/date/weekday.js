// Source: http://unicode.org/cldr/trac/browser/tags/release-27/common/main/en.xml#L1847
module["exports"] = {
  wide: [
    "Chủ nhật",
    "Thứ hai",
    "Th<PERSON> ba",
    "<PERSON><PERSON><PERSON> tư",
    "<PERSON><PERSON><PERSON> năm",
    "<PERSON><PERSON><PERSON> sáu",
    "<PERSON><PERSON><PERSON> bảy"
  ],
  // Property "wide_context" is optional, if not set then "wide" will be used instead
  // It is used to specify a word in context, which may differ from a stand-alone word
  wide_context: [
    "Chủ nhật",
    "Thứ hai",
    "Thứ ba",
    "Thứ tư",
    "<PERSON><PERSON><PERSON> năm",
    "<PERSON><PERSON><PERSON> sáu",
    "Th<PERSON> bảy"
  ],
  abbr: [
    "CN",
    "T2",
    "T3",
    "T4",
    "T5",
    "T6",
    "T7",
  ],
  // Property "abbr_context" is optional, if not set then "abbr" will be used instead
  // It is used to specify a word in context, which may differ from a stand-alone word
  abbr_context: [
    "C<PERSON>N<PERSON>ật",
    "<PERSON><PERSON><PERSON> 2",
    "<PERSON><PERSON><PERSON> 3",
    "<PERSON><PERSON><PERSON> 4",
    "<PERSON><PERSON><PERSON> 5",
    "<PERSON><PERSON><PERSON> 6",
    "T<PERSON><PERSON> 7",
  ]
};

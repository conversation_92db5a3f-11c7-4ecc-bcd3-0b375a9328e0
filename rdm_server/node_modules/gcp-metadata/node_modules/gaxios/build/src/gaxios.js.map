{"version": 3, "file": "gaxios.js", "sourceRoot": "", "sources": ["../../src/gaxios.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;;;;AAEjC,oDAA4B;AAE5B,iCAA0C;AAC1C,4DAAmC;AACnC,8DAA6B;AAC7B,0DAAiC;AACjC,6BAAwB;AAExB,qCAOkB;AAClB,mCAAuC;AAGvC,uDAAuD;AAEvD,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,oBAAS,CAAC;AAEpD,SAAS,SAAS;IAChB,OAAO,OAAO,MAAM,KAAK,WAAW,IAAI,CAAC,CAAC,MAAM,CAAC;AACnD,CAAC;AAED,SAAS,QAAQ;IACf,OAAO,SAAS,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AACvC,CAAC;AAED,SAAS,SAAS;IAChB,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACvC,CAAC;AAED,SAAS,SAAS,CAAC,OAAsB,EAAE,MAAc;IACvD,OAAO,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACtC,CAAC;AAED,SAAS,SAAS,CAAC,OAAsB,EAAE,MAAc;IACvD,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;IAC9B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,KAAI,EAAE,CAAC,EAAE;QACrD,IAAI,MAAM,KAAK,GAAG,CAAC,WAAW,EAAE,EAAE;YAChC,OAAO,OAAO,CAAC,OAAQ,CAAC,GAAG,CAAC,CAAC;SAC9B;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,IAAI,eAAoB,CAAC;AAEzB,SAAS,SAAS;;IAChB,MAAM,KAAK,GACT,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,0CAAE,WAAW;SACzB,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,0CAAE,WAAW,CAAA;SACzB,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,0CAAE,UAAU,CAAA;SACxB,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,0CAAE,UAAU,CAAA,CAAC;IAC3B,IAAI,KAAK,EAAE;QACT,eAAe,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;KAChD;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AACD,SAAS,EAAE,CAAC;AAEZ,SAAS,SAAS,CAAC,GAAW;;IAC5B,MAAM,UAAU,GAAG,MAAA,OAAO,CAAC,GAAG,CAAC,QAAQ,mCAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;IAChE,IAAI,CAAC,UAAU,EAAE;QACf,OAAO,KAAK,CAAC;KACd;IACD,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC1C,MAAM,SAAS,GAAG,IAAI,SAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,OAAO,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC9B,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YAC/C,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAChC,OAAO,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;SACzC;aAAM;YACL,OAAO,GAAG,KAAK,SAAS,CAAC,MAAM,IAAI,GAAG,KAAK,SAAS,CAAC,QAAQ,CAAC;SAC/D;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,wEAAwE;AACxE,wDAAwD;AACxD,SAAS,QAAQ,CAAC,GAAW;IAC3B,wFAAwF;IACxF,IAAI,SAAS,CAAC,GAAG,CAAC,EAAE;QAClB,OAAO,SAAS,CAAC;QACjB,kHAAkH;KACnH;SAAM;QACL,OAAO,SAAS,EAAE,CAAC;KACpB;AACH,CAAC;AAED,MAAa,MAAM;IAQjB;;;OAGG;IACH,YAAY,QAAwB;QAX1B,eAAU,GAAG,IAAI,GAAG,EAA+C,CAAC;QAY5E,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,EAAE,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAO,CAAU,OAAsB,EAAE;QAC7C,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAEO,KAAK,CAAC,eAAe,CAC3B,IAAmB;QAEnB,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,IAAI,KAAK,CAAC;QACpD,MAAM,GAAG,GAAG,CAAC,MAAM,SAAS,CAAC,IAAI,CAAC,GAAI,EAAE,IAAI,CAAC,CAAkB,CAAC;QAChE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC,iBAAiB,CAAI,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,QAAQ,CACtB,OAAsB,EAAE;QAExB,IAAI;YACF,IAAI,kBAAqC,CAAC;YAC1C,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,CACrC,IAAI,EACJ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC,CAAC;aACH;iBAAM;gBACL,kBAAkB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;aACvD;YACD,IAAI,CAAC,IAAI,CAAC,cAAe,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE;gBACpD,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE;oBAClC,IAAI,QAAQ,GAAG,EAAE,CAAC;oBAClB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;wBACzB,kBAAkB,CAAC,IAAe,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;4BACrD,QAAQ,IAAI,KAAK,CAAC;wBACpB,CAAC,CAAC,CAAC;wBACF,kBAAkB,CAAC,IAAe,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBACzD,CAAC,CAAC,CAAC;oBACH,kBAAkB,CAAC,IAAI,GAAG,QAAa,CAAC;iBACzC;gBACD,MAAM,IAAI,oBAAW,CACnB,mCAAmC,kBAAkB,CAAC,MAAM,EAAE,EAC9D,IAAI,EACJ,kBAAkB,CACnB,CAAC;aACH;YACD,OAAO,kBAAkB,CAAC;SAC3B;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,GAAG,GAAG,CAAgB,CAAC;YAC7B,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC;YAClB,MAAM,EAAC,WAAW,EAAE,MAAM,EAAC,GAAG,MAAM,IAAA,sBAAc,EAAC,GAAG,CAAC,CAAC;YACxD,IAAI,WAAW,IAAI,MAAM,EAAE;gBACzB,GAAG,CAAC,MAAM,CAAC,WAAY,CAAC,mBAAmB;oBACzC,MAAM,CAAC,WAAY,CAAC,mBAAmB,CAAC;gBAC1C,OAAO,IAAI,CAAC,QAAQ,CAAI,GAAG,CAAC,MAAM,CAAC,CAAC;aACrC;YACD,MAAM,GAAG,CAAC;SACX;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAC3B,IAAmB,EACnB,GAAkB;QAElB,QAAQ,IAAI,CAAC,YAAY,EAAE;YACzB,KAAK,QAAQ;gBACX,OAAO,GAAG,CAAC,IAAI,CAAC;YAClB,KAAK,MAAM,CAAC,CAAC;gBACX,IAAI,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC;gBAC5B,IAAI;oBACF,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;iBACzB;gBAAC,WAAM;oBACN,WAAW;iBACZ;gBACD,OAAO,IAAU,CAAC;aACnB;YACD,KAAK,aAAa;gBAChB,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC;YAC3B,KAAK,MAAM;gBACT,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC;YACpB;gBACE,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC;SACrB;IACH,CAAC;IAED;;;OAGG;IACK,YAAY,CAAC,OAAsB;QACzC,MAAM,IAAI,GAAG,IAAA,gBAAM,EAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACrC;QAED,6CAA6C;QAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;QAC7C,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC;SAC/B;QAED,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC;QACvE,IAAI,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YACtD,IAAI,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/D,IAAI,qBAAqB,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACzC,qBAAqB,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACxD;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAClD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,qBAAqB,CAAC;SACtD;QAED,IAAI,OAAO,OAAO,CAAC,gBAAgB,KAAK,QAAQ,EAAE;YAChD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,gBAAgB,CAAC;SACtC;QAED,IAAI,OAAO,OAAO,CAAC,YAAY,KAAK,QAAQ,EAAE;YAC5C,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC;SACpC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;QAClC,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,MAAM,UAAU,GACd,OAAO,QAAQ,KAAK,WAAW;gBAC7B,CAAC,CAAC,KAAK;gBACP,CAAC,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,aAAY,QAAQ,CAAC;YACrC,IAAI,mBAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;aACvB;iBAAM,IAAI,SAAS,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACpD,+CAA+C;gBAC/C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gBACtB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,cAAc,CAAC,EAAE;oBACpC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC;iBACnD;aACF;iBAAM,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACxC,gEAAgE;gBAChE,kEAAkE;gBAClE,IAAI,CAAC,UAAU,EAAE;oBACf,IACE,SAAS,CAAC,IAAI,EAAE,cAAc,CAAC;wBAC/B,mCAAmC,EACnC;wBACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBAC9C;yBAAM;wBACL,iDAAiD;wBACjD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,cAAc,CAAC,EAAE;4BACpC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC;yBACnD;wBACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBACvC;iBACF;aACF;iBAAM;gBACL,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;aACvB;SACF;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC;QACjE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC;QAChD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,YAAY,KAAK,MAAM,EAAE;YAC3D,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,kBAAkB,CAAC;SAC7C;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC;QAEnC,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,KAAK,EAAE;YACT,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBAC9B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aACzC;iBAAM;gBACL,gDAAgD;gBAChD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE;oBACzB,MAAM,SAAS,GAAG,IAAI,SAAG,CAAC,KAAK,CAAC,CAAC;oBACjC,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,CAAC;wBAC/B,IAAI,EAAE,SAAS,CAAC,IAAI;wBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;wBACpB,QAAQ,EAAE,SAAS,CAAC,QAAQ;wBAC5B,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,GAAG,EAAE,IAAI,CAAC,GAAG;qBACd,CAAC,CAAC;iBACJ;qBAAM;oBACL,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;iBACzC;gBACD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,KAAM,CAAC,CAAC;aACzC;SACF;aAAM,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE;YAChC,6BAA6B;YAC7B,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACjC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAC5C;iBAAM;gBACL,IAAI,CAAC,KAAK,GAAG,IAAI,aAAU,CAAC;oBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,GAAG,EAAE,IAAI,CAAC,GAAG;iBACd,CAAC,CAAC;gBACH,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAM,CAAC,CAAC;aAC5C;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACK,cAAc,CAAC,MAAc;QACnC,OAAO,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG,GAAG,CAAC;IACvC,CAAC;IAED;;;OAGG;IACK,gBAAgB,CAAC,MAA0C;QACjE,OAAO,qBAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAEO,iBAAiB,CACvB,IAAmB,EACnB,GAAkB,EAClB,IAAQ;QAER,oDAAoD;QACpD,MAAM,OAAO,GAAG,EAAa,CAAC;QAC9B,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YACjC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,MAAM,EAAE,IAAI;YACZ,IAAI,EAAE,IAAS;YACf,OAAO;YACP,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,UAAU,EAAE,GAAG,CAAC,UAAU;YAE1B,qBAAqB;YACrB,OAAO,EAAE;gBACP,WAAW,EAAE,GAAG,CAAC,GAAG;aACrB;SACF,CAAC;IACJ,CAAC;CACF;AApQD,wBAoQC"}
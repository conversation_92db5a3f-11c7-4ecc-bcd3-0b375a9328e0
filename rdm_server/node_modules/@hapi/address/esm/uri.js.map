{"version": 3, "file": "uri.js", "sourceRoot": "", "sources": ["../src/uri.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AAmBjD,SAAS,QAAQ;IACb,MAAM,OAAO,GAAG,EAAa,CAAC;IAE9B,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,qDAAqD;IACnF,MAAM,YAAY,GAAG,GAAG,GAAG,QAAQ,GAAG,GAAG,CAAC;IAE1C,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC,qDAAqD;IACpF,MAAM,SAAS,GAAG,uBAAuB,CAAC,CAAC,+EAA+E;IAC1H,MAAM,UAAU,GAAG,GAAG,GAAG,QAAQ,CAAC,CAAC,kCAAkC;IACrE,MAAM,KAAK,GAAG,UAAU,GAAG,UAAU,GAAG,SAAS,GAAG,IAAI,CAAC,CAAC,4DAA4D;IACtH,MAAM,SAAS,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,CAAC;IACpC,MAAM,SAAS,GAAG,oDAAoD,CAAC,CAAC,iIAAiI;IAEzM,OAAO,CAAC,WAAW,GAAG,KAAK,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC,oEAAoE;IAErI;;;;;;;;;;;;MAYE;IAEF,MAAM,GAAG,GAAG,YAAY,GAAG,OAAO,CAAC;IACnC,MAAM,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,CAAC,WAAW,GAAG,GAAG,CAAC;IACvE,MAAM,UAAU,GAAG,KAAK,GAAG,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC;IAChD,MAAM,WAAW,GAAG,OAAO,GAAG,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC;IACnD,MAAM,WAAW,GAAG,KAAK,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC;IACnE,MAAM,YAAY,GAAG,QAAQ,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC;IACzF,MAAM,UAAU,GAAG,QAAQ,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC;IACvF,MAAM,UAAU,GAAG,QAAQ,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;IAChF,MAAM,WAAW,GAAG,QAAQ,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC;IACrE,MAAM,YAAY,GAAG,QAAQ,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;IACrE,MAAM,YAAY,GAAG,QAAQ,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC;IAE/D,OAAO,CAAC,QAAQ,GAAG,yBAAyB,CAAC,CAAC,yEAAyE;IACvH,OAAO,CAAC,QAAQ,GAAG,2CAA2C,CAAC,CAAC,sGAAsG;IACtK,OAAO,CAAC,WAAW;QACf,KAAK;YACL,UAAU;YACV,GAAG;YACH,WAAW;YACX,GAAG;YACH,WAAW;YACX,GAAG;YACH,YAAY;YACZ,GAAG;YACH,UAAU;YACV,GAAG;YACH,UAAU;YACV,GAAG;YACH,WAAW;YACX,GAAG;YACH,YAAY;YACZ,GAAG;YACH,YAAY;YACZ,GAAG,CAAC;IACR,OAAO,CAAC,SAAS,GAAG,GAAG,GAAG,YAAY,GAAG,OAAO,GAAG,UAAU,GAAG,SAAS,GAAG,KAAK,CAAC,CAAC,mEAAmE;IAEtJ,OAAO,CAAC,MAAM,GAAG,2BAA2B,CAAC,CAAC,sDAAsD;IACpG,OAAO,CAAC,WAAW,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAEjD,MAAM,QAAQ,GAAG,GAAG,GAAG,UAAU,GAAG,UAAU,GAAG,SAAS,GAAG,KAAK,CAAC,CAAC,8DAA8D;IAClI,MAAM,SAAS,GAAG,QAAQ,GAAG,OAAO,CAAC,WAAW,GAAG,GAAG,GAAG,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,oDAAoD;IACzI,MAAM,OAAO,GAAG,GAAG,GAAG,UAAU,GAAG,UAAU,GAAG,SAAS,GAAG,UAAU,CAAC,CAAC,wDAAwD;IAChI,MAAM,IAAI,GAAG,KAAK,GAAG,SAAS,GAAG,GAAG,GAAG,OAAO,CAAC,WAAW,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC,6CAA6C;IAC/H,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,gBAAgB;IACrC,MAAM,SAAS,GAAG,KAAK,GAAG,QAAQ,GAAG,KAAK,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,mDAAmD;IAC7H,MAAM,gBAAgB,GAAG,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,GAAG,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;IAElF;;;;;;;;;;;MAWE;IAEF,MAAM,OAAO,GAAG,SAAS,GAAG,GAAG,CAAC;IAChC,MAAM,SAAS,GAAG,SAAS,GAAG,GAAG,CAAC;IAClC,MAAM,WAAW,GAAG,GAAG,GAAG,UAAU,GAAG,UAAU,GAAG,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC;IAC3E,MAAM,SAAS,GAAG,EAAE,CAAC;IACrB,MAAM,WAAW,GAAG,QAAQ,GAAG,OAAO,GAAG,IAAI,CAAC;IAC9C,MAAM,YAAY,GAAG,QAAQ,GAAG,SAAS,GAAG,WAAW,GAAG,IAAI,CAAC;IAC/D,MAAM,YAAY,GAAG,SAAS,GAAG,WAAW,CAAC;IAC7C,MAAM,YAAY,GAAG,WAAW,GAAG,WAAW,CAAC;IAC/C,MAAM,iBAAiB,GAAG,cAAc,GAAG,OAAO,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC,mBAAmB;IAE3F,kCAAkC;IAElC,OAAO,CAAC,QAAQ;QACZ,KAAK;YACL,WAAW;YACX,SAAS;YACT,WAAW;YACX,GAAG;YACH,GAAG;YACH,YAAY;YACZ,GAAG;YACH,YAAY;YACZ,GAAG;YACH,iBAAiB;YACjB,GAAG,CAAC;IACR,OAAO,CAAC,eAAe;QACnB,KAAK,GAAG,WAAW,GAAG,gBAAgB,GAAG,WAAW,GAAG,GAAG,GAAG,GAAG,GAAG,YAAY,GAAG,GAAG,GAAG,YAAY,GAAG,GAAG,CAAC;IAE/G,2FAA2F;IAE3F,OAAO,CAAC,WAAW;QACf,KAAK;YACL,WAAW;YACX,SAAS;YACT,WAAW;YACX,GAAG;YACH,GAAG;YACH,YAAY;YACZ,GAAG;YACH,YAAY;YACZ,GAAG;YACH,SAAS;YACT,GAAG,CAAC;IACR,OAAO,CAAC,kBAAkB;QACtB,KAAK;YACL,WAAW;YACX,gBAAgB;YAChB,WAAW;YACX,GAAG;YACH,GAAG;YACH,YAAY;YACZ,GAAG;YACH,YAAY;YACZ,GAAG;YACH,SAAS;YACT,GAAG,CAAC;IAER,iCAAiC;IACjC,6CAA6C;IAE7C,OAAO,CAAC,KAAK,GAAG,GAAG,GAAG,KAAK,GAAG,iBAAiB,CAAC,CAAC,kEAAkE;IACnH,OAAO,CAAC,uBAAuB,GAAG,GAAG,GAAG,KAAK,GAAG,uBAAuB,CAAC;IAExE,oCAAoC;IAEpC,OAAO,CAAC,QAAQ,GAAG,GAAG,GAAG,KAAK,GAAG,UAAU,CAAC;IAE5C,OAAO,OAAO,CAAC;AACnB,CAAC;AAED,MAAM,OAAO,GAAG,QAAQ,EAAE,CAAC;AAE3B,MAAM,CAAC,MAAM,UAAU,GAAG;IACtB,MAAM,EAAE,OAAO,CAAC,QAAQ;IACxB,MAAM,EAAE,OAAO,CAAC,QAAQ;IACxB,IAAI,EAAE,OAAO,CAAC,WAAW;IACzB,IAAI,EAAE,OAAO,CAAC,WAAW;IACzB,SAAS,EAAE,OAAO,CAAC,SAAS;CAC/B,CAAC;AAEF,SAAS,WAAW,CAAC,OAAgB;IACjC,MAAM,GAAG,GAAG,OAAO,CAAC;IAEpB,uBAAuB;IAEvB,MAAM,KAAK,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;IACzF,MAAM,MAAM,GAAG,QAAQ,GAAG,KAAK,GAAG,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;IAEtE,8DAA8D;IAE9D,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC;IAE3E,IAAI,OAAO,CAAC,YAAY,EAAE;QACtB,OAAO,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC;KAClC;IAED,iBAAiB;IAEjB,IAAI,YAAY,GAAG,EAAE,CAAC;IACtB,IAAI,OAAO,CAAC,MAAM,EAAE;QAChB,MAAM,CACF,OAAO,CAAC,MAAM,YAAY,MAAM,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EACvG,2CAA2C,CAC9C,CAAC;QAEF,MAAM,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,8CAA8C,CAAC,CAAC;QAE5E,kEAAkE;QAElE,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACrC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,CACF,MAAM,YAAY,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EACtD,qBAAqB,GAAG,CAAC,GAAG,6BAA6B,CAC5D,CAAC;YAEF,IAAI,MAAM,YAAY,MAAM,EAAE;gBAC1B,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;aAC7C;iBAAM;gBACH,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,qBAAqB,GAAG,CAAC,GAAG,yBAAyB,CAAC,CAAC;gBAC5F,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;aACxC;SACJ;QAED,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACvC;IAED,4DAA4D;IAE5D,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;IACtE,MAAM,QAAQ,GAAG,KAAK,GAAG,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;IACpG,MAAM,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,GAAG,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC1F,OAAO,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE,YAAY,CAAC,CAAC;AAC/C,CAAC;AAaD,SAAS,IAAI,CAAC,GAAW,EAAE,SAAiB,IAAI;IAC5C,GAAG,GAAG,8DAA8D,GAAG,EAAE,CAAC,CAAC,0FAA0F;IAErK,OAAO;QACH,GAAG;QACH,KAAK,EAAE,IAAI,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC;QAC7B,MAAM;KACT,CAAC;AACN,CAAC;AAED,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;AAExC;;;;;;GAMG;AACH,MAAM,UAAU,QAAQ,CAAC,UAAmB,EAAE;IAC1C,IACI,OAAO,CAAC,MAAM;QACd,OAAO,CAAC,aAAa;QACrB,OAAO,CAAC,YAAY;QACpB,OAAO,CAAC,wBAAwB;QAChC,OAAO,CAAC,MAAM,EAChB;QACE,OAAO,WAAW,CAAC,OAAO,CAAC,CAAC;KAC/B;IAED,OAAO,eAAe,CAAC;AAC3B,CAAC"}
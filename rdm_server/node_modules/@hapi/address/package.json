{"name": "@hapi/address", "description": "Email address and domain validation", "version": "5.1.1", "repository": "git://github.com/hapijs/address", "main": "./dist/index.js", "module": "./esm/index.js", "typings": "./dist/index.d.ts", "files": ["dist", "src", "esm"], "keywords": ["email", "domain", "address", "validation"], "engines": {"node": ">=14.0.0"}, "dependencies": {"@hapi/hoek": "^11.0.2"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/lab": "^25.1.2", "@types/node": "^14.18.36", "@typescript-eslint/eslint-plugin": "^5.52.0", "@typescript-eslint/parser": "^5.52.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.4", "typescript": "4.9.x"}, "scripts": {"test": "lab -t 100 -L", "test-cov-html": "lab -t 100 -L -r html -o coverage.html", "dist": "rm -rf dist/* && rm -rf esm/* && tsc --module commonjs --outdir dist && tsc --module es6 --outdir esm", "format": "prettier --write '**/*.ts'"}, "license": "BSD-3-<PERSON><PERSON>"}
{"name": "@hapi/pinpoint", "description": "Return the filename and line number of the calling function", "version": "2.0.1", "repository": "git://github.com/hapijs/pinpoint", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "keywords": ["utilities"], "dependencies": {}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/lab": "^25.1.2", "@types/node": "^14.18.36", "typescript": "4.0.x"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}
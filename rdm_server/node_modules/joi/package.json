{"name": "joi", "description": "Object schema validation", "version": "18.0.1", "repository": {"url": "git://github.com/hapijs/joi", "type": "git"}, "engines": {"node": ">= 20"}, "main": "lib/index.js", "types": "lib/index.d.ts", "browser": "dist/joi-browser.min.js", "files": ["lib/**/*", "dist/*"], "keywords": ["schema", "validation"], "dependencies": {"@hapi/address": "^5.1.1", "@hapi/formula": "^3.0.2", "@hapi/hoek": "^11.0.7", "@hapi/pinpoint": "^2.0.1", "@hapi/tlds": "^1.1.1", "@hapi/topo": "^6.0.2", "@standard-schema/spec": "^1.0.0"}, "devDependencies": {"@hapi/bourne": "^3.0.0", "@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "^7.0.0", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x", "@hapi/lab": "^26.0.0", "@types/node": "^20.17.47", "typescript": "^5.8.3"}, "scripts": {"prepublishOnly": "cd browser && npm install && npm run build", "test": "lab -t 100 -a @hapi/code -L -Y", "test-cov-html": "lab -r html -o coverage.html -a @hapi/code"}, "license": "BSD-3-<PERSON><PERSON>"}
# TaskFlow 客户端调试指南

## 🚨 "资源未找到" 错误解决方案

### 问题分析
客户端报错"资源未找到"通常表示：
1. 服务器地址配置错误
2. API端点路径不正确
3. Parse SDK配置问题
4. 网络连接问题

## 🔧 正确的客户端配置

### iOS Parse SDK 配置 (推荐)

```swift
import Parse

// 方法1: 标准配置 (推荐)
func configureParse() {
    let configuration = ParseClientConfiguration {
        $0.applicationId = "taskflow-app-id"
        $0.clientKey = "taskflow-js-key"  // 注意：使用 clientKey，不是 restAPIKey
        $0.server = "http://************:3000/parse"
    }
    Parse.initialize(with: configuration)
}

// 方法2: 如果方法1不工作，尝试这个
func configureParseAlternative() {
    Parse.initialize(
        with: ParseClientConfiguration {
            $0.applicationId = "taskflow-app-id"
            $0.clientKey = "taskflow-js-key"
            $0.server = "http://************:3000/parse"
            $0.isLocalDatastoreEnabled = false
        }
    )
}
```

### 重要配置说明

1. **服务器地址**: `http://************:3000/parse`
   - 必须包含 `/parse` 路径
   - 不要添加版本号 (如 `/1`)

2. **认证密钥**:
   - **Application ID**: `taskflow-app-id`
   - **Client Key**: `taskflow-js-key` (iOS SDK使用)
   - **REST API Key**: `taskflow-rest-key` (仅用于直接HTTP请求)

## 🧪 调试步骤

### 步骤1: 验证网络连接

```swift
// 测试基本网络连接
func testNetworkConnection() {
    guard let url = URL(string: "http://************:3000/health") else { return }
    
    URLSession.shared.dataTask(with: url) { data, response, error in
        if let error = error {
            print("网络错误: \(error)")
            return
        }
        
        if let httpResponse = response as? HTTPURLResponse {
            print("HTTP状态码: \(httpResponse.statusCode)")
        }
        
        if let data = data, let responseString = String(data: data, encoding: .utf8) {
            print("服务器响应: \(responseString)")
        }
    }.resume()
}
```

### 步骤2: 测试Parse服务器连接

```swift
// 测试Parse服务器是否可达
func testParseServerConnection() {
    guard let url = URL(string: "http://************:3000/parse") else { return }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    request.setValue("taskflow-app-id", forHTTPHeaderField: "X-Parse-Application-Id")
    
    URLSession.shared.dataTask(with: request) { data, response, error in
        if let error = error {
            print("Parse服务器连接错误: \(error)")
            return
        }
        
        if let httpResponse = response as? HTTPURLResponse {
            print("Parse服务器HTTP状态码: \(httpResponse.statusCode)")
            // 期望返回401 (unauthorized) 表示服务器存在但需要认证
        }
    }.resume()
}
```

### 步骤3: 测试用户登录

```swift
// 方法1: 使用Parse SDK (推荐)
func loginWithParseSDK() {
    PFUser.logInWithUsername(inBackground: "demo_user", password: "Test123!") { (user, error) in
        if let user = user {
            print("✅ 登录成功!")
            print("用户ID: \(user.objectId ?? "")")
            print("用户名: \(user.username ?? "")")
            print("邮箱: \(user.email ?? "")")
        } else if let error = error {
            print("❌ 登录失败: \(error.localizedDescription)")
            print("错误代码: \((error as NSError).code)")
            
            // 详细错误分析
            switch (error as NSError).code {
            case 100:
                print("提示: 网络连接问题")
            case 101:
                print("提示: 用户名或密码错误")
            case 124:
                print("提示: 请求超时")
            default:
                print("提示: 其他错误")
            }
        }
    }
}

// 方法2: 直接HTTP请求 (用于调试)
func loginWithHTTPRequest() {
    guard let url = URL(string: "http://************:3000/parse/login") else { return }
    
    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    request.setValue("taskflow-app-id", forHTTPHeaderField: "X-Parse-Application-Id")
    request.setValue("taskflow-rest-key", forHTTPHeaderField: "X-Parse-REST-API-Key")
    
    let loginData = [
        "username": "demo_user",
        "password": "Test123!"
    ]
    
    do {
        request.httpBody = try JSONSerialization.data(withJSONObject: loginData)
    } catch {
        print("JSON序列化错误: \(error)")
        return
    }
    
    URLSession.shared.dataTask(with: request) { data, response, error in
        if let error = error {
            print("HTTP请求错误: \(error)")
            return
        }
        
        if let httpResponse = response as? HTTPURLResponse {
            print("HTTP状态码: \(httpResponse.statusCode)")
        }
        
        if let data = data, let responseString = String(data: data, encoding: .utf8) {
            print("登录响应: \(responseString)")
        }
    }.resume()
}
```

## 🔍 常见问题排查

### 问题1: "资源未找到" (404错误)

**可能原因**:
- 服务器地址错误
- 缺少 `/parse` 路径
- 添加了错误的版本号

**解决方案**:
```swift
// ❌ 错误配置
$0.server = "http://************:3000"           // 缺少 /parse
$0.server = "http://************:3000/parse/1"   // 多余的版本号

// ✅ 正确配置
$0.server = "http://************:3000/parse"
```

### 问题2: "未授权" (401错误)

**可能原因**:
- Application ID 错误
- Client Key 错误

**解决方案**:
```swift
// 确保使用正确的密钥
$0.applicationId = "taskflow-app-id"
$0.clientKey = "taskflow-js-key"  // 不是 restAPIKey
```

### 问题3: 网络连接超时

**可能原因**:
- 服务器未运行
- 防火墙阻止
- 网络配置问题

**解决方案**:
1. 确认服务器运行状态
2. 检查网络连接
3. 尝试使用localhost (如果在模拟器中)

## 📱 完整的测试代码

```swift
import Parse

class ParseTestManager {
    
    static func runAllTests() {
        print("🚀 开始Parse连接测试...")
        
        // 1. 配置Parse
        configureParse()
        
        // 2. 测试网络连接
        testNetworkConnection()
        
        // 3. 延迟测试登录 (等待网络测试完成)
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            testLogin()
        }
    }
    
    private static func configureParse() {
        let configuration = ParseClientConfiguration {
            $0.applicationId = "taskflow-app-id"
            $0.clientKey = "taskflow-js-key"
            $0.server = "http://************:3000/parse"
        }
        Parse.initialize(with: configuration)
        print("✅ Parse配置完成")
    }
    
    private static func testNetworkConnection() {
        guard let url = URL(string: "http://************:3000/health") else { return }
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ 网络连接失败: \(error.localizedDescription)")
                } else if let httpResponse = response as? HTTPURLResponse {
                    if httpResponse.statusCode == 200 {
                        print("✅ 服务器连接正常")
                    } else {
                        print("⚠️ 服务器响应异常: \(httpResponse.statusCode)")
                    }
                }
            }
        }.resume()
    }
    
    private static func testLogin() {
        print("🔐 测试用户登录...")
        
        PFUser.logInWithUsername(inBackground: "demo_user", password: "Test123!") { (user, error) in
            DispatchQueue.main.async {
                if let user = user {
                    print("✅ 登录成功!")
                    print("   用户ID: \(user.objectId ?? "")")
                    print("   用户名: \(user.username ?? "")")
                    print("   邮箱: \(user.email ?? "")")
                    
                    // 测试数据访问
                    testDataAccess()
                } else if let error = error {
                    print("❌ 登录失败: \(error.localizedDescription)")
                    print("   错误代码: \((error as NSError).code)")
                }
            }
        }
    }
    
    private static func testDataAccess() {
        print("📊 测试数据访问...")
        
        let query = PFQuery(className: "Task")
        query.limit = 5
        
        query.findObjectsInBackground { (objects, error) in
            DispatchQueue.main.async {
                if let tasks = objects {
                    print("✅ 数据访问成功! 获取到 \(tasks.count) 个任务")
                } else if let error = error {
                    print("❌ 数据访问失败: \(error.localizedDescription)")
                }
            }
        }
    }
}

// 在AppDelegate或SceneDelegate中调用
// ParseTestManager.runAllTests()
```

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：

1. **错误信息**: 完整的错误消息
2. **网络测试结果**: `http://************:3000/health` 的响应
3. **Parse测试结果**: `http://************:3000/parse` 的响应
4. **客户端配置**: 使用的Parse SDK版本和配置代码
5. **设备信息**: iOS版本、设备类型、模拟器还是真机

---

**当前服务器状态**: ✅ 运行中
**测试端点**: http://************:3000/health
**Parse端点**: http://************:3000/parse

/**
 * 统一响应处理中间件
 * 确保所有API响应格式一致
 */

const moment = require('moment');
const { ApiResponseSchemas } = require('../models');

// 错误代码定义
const ErrorCodes = {
  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  ACCOUNT_DISABLED: 'ACCOUNT_DISABLED',
  
  // 验证错误
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT: 'INVALID_FORMAT',
  
  // 资源错误
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  
  // 服务器错误
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  
  // 业务逻辑错误
  INVALID_OPERATION: 'INVALID_OPERATION',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  DEPENDENCY_ERROR: 'DEPENDENCY_ERROR'
};

// 错误消息映射（支持多语言）
const ErrorMessages = {
  [ErrorCodes.UNAUTHORIZED]: {
    en: 'Authentication required',
    zh: '需要身份验证'
  },
  [ErrorCodes.INVALID_CREDENTIALS]: {
    en: 'Invalid username or password',
    zh: '用户名或密码错误'
  },
  [ErrorCodes.TOKEN_EXPIRED]: {
    en: 'Session has expired, please login again',
    zh: '会话已过期，请重新登录'
  },
  [ErrorCodes.ACCOUNT_DISABLED]: {
    en: 'Account has been disabled',
    zh: '账户已被禁用'
  },
  [ErrorCodes.VALIDATION_ERROR]: {
    en: 'Validation failed',
    zh: '数据验证失败'
  },
  [ErrorCodes.NOT_FOUND]: {
    en: 'Resource not found',
    zh: '资源未找到'
  },
  [ErrorCodes.ALREADY_EXISTS]: {
    en: 'Resource already exists',
    zh: '资源已存在'
  },
  [ErrorCodes.PERMISSION_DENIED]: {
    en: 'Permission denied',
    zh: '权限不足'
  },
  [ErrorCodes.INTERNAL_ERROR]: {
    en: 'Internal server error',
    zh: '服务器内部错误'
  }
};

// 响应处理中间件
function responseHandler(req, res, next) {
  // 成功响应
  res.success = function(data = null, message = null) {
    const response = {
      success: true,
      timestamp: moment().toISOString(),
      requestId: req.id
    };
    
    if (message) {
      response.message = message;
    }
    
    if (data !== null) {
      response.data = data;
    }
    
    // 开发模式下添加调试信息
    if (process.env.NODE_ENV === 'development') {
      response.debug = {
        endpoint: `${req.method} ${req.path}`,
        duration: Date.now() - req.startTime
      };
    }
    
    return res.json(response);
  };
  
  // 分页响应
  res.paginated = function(data, pagination, message = null) {
    const response = {
      success: true,
      timestamp: moment().toISOString(),
      requestId: req.id,
      data: data,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total: pagination.total,
        pages: Math.ceil(pagination.total / pagination.limit),
        hasNext: pagination.page < Math.ceil(pagination.total / pagination.limit),
        hasPrev: pagination.page > 1
      }
    };
    
    if (message) {
      response.message = message;
    }
    
    return res.json(response);
  };
  
  // 错误响应
  res.error = function(code, message = null, details = null, statusCode = 400) {
    const lang = req.headers['accept-language']?.startsWith('zh') ? 'zh' : 'en';
    
    const response = {
      success: false,
      timestamp: moment().toISOString(),
      requestId: req.id,
      error: {
        code: code,
        message: message || ErrorMessages[code]?.[lang] || ErrorMessages[code]?.en || 'Unknown error'
      }
    };
    
    if (details) {
      response.error.details = details;
    }
    
    // 开发模式下添加调试信息
    if (process.env.NODE_ENV === 'development') {
      response.debug = {
        endpoint: `${req.method} ${req.path}`,
        duration: Date.now() - req.startTime,
        stack: details?.stack
      };
    }
    
    return res.status(statusCode).json(response);
  };
  
  // 常用错误响应快捷方法
  res.unauthorized = function(message = null, details = null) {
    return res.error(ErrorCodes.UNAUTHORIZED, message, details, 401);
  };
  
  res.forbidden = function(message = null, details = null) {
    return res.error(ErrorCodes.PERMISSION_DENIED, message, details, 403);
  };
  
  res.notFound = function(message = null, details = null) {
    return res.error(ErrorCodes.NOT_FOUND, message, details, 404);
  };
  
  res.validationError = function(details = null) {
    return res.error(ErrorCodes.VALIDATION_ERROR, null, details, 400);
  };
  
  res.internalError = function(message = null, details = null) {
    return res.error(ErrorCodes.INTERNAL_ERROR, message, details, 500);
  };
  
  // 记录请求开始时间
  req.startTime = Date.now();
  
  next();
}

// 全局错误处理中间件
function errorHandler(err, req, res, next) {
  console.error('全局错误处理:', err);
  
  // Joi验证错误
  if (err.isJoi) {
    const details = err.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));
    
    return res.validationError(details);
  }
  
  // Parse错误
  if (err.code) {
    switch (err.code) {
      case 101:
        return res.error(ErrorCodes.INVALID_CREDENTIALS);
      case 124:
        return res.error(ErrorCodes.TOKEN_EXPIRED);
      case 125:
        return res.error(ErrorCodes.INVALID_CREDENTIALS);
      case 137:
        return res.error(ErrorCodes.ALREADY_EXISTS, '用户名已存在');
      case 203:
        return res.error(ErrorCodes.ALREADY_EXISTS, '邮箱已存在');
      default:
        return res.internalError('Parse服务器错误', { parseCode: err.code, parseMessage: err.message });
    }
  }
  
  // MongoDB错误
  if (err.name === 'MongoError' || err.name === 'MongoServerError') {
    return res.error(ErrorCodes.DATABASE_ERROR, '数据库操作失败', { mongoCode: err.code });
  }
  
  // 默认内部错误
  return res.internalError('服务器内部错误', {
    name: err.name,
    message: err.message,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
}

// 404处理中间件
function notFoundHandler(req, res) {
  res.notFound('API端点不存在', {
    method: req.method,
    path: req.path,
    availableEndpoints: [
      'POST /api/v1/auth/login',
      'GET /api/v1/auth/me',
      'GET /api/v1/tasks',
      'POST /api/v1/tasks',
      'GET /api/v1/habits',
      'POST /api/v1/habits'
    ]
  });
}

// 开发模式功能
const DevModeFeatures = {
  // 记住的账号密码
  savedCredentials: {
    demo: {
      email: '<EMAIL>',
      password: 'Test123!',
      username: 'demo_user'
    },
    admin: {
      email: '<EMAIL>',
      password: 'Admin123!',
      username: 'admin'
    },
    test: {
      email: '<EMAIL>',
      password: 'Test123!',
      username: 'test_user'
    }
  },
  
  // 获取保存的凭据
  getSavedCredentials() {
    return this.savedCredentials;
  },
  
  // 快速登录端点
  quickLogin(req, res) {
    const { account } = req.params;
    const credentials = this.savedCredentials[account];
    
    if (!credentials) {
      return res.notFound('保存的账户不存在', {
        availableAccounts: Object.keys(this.savedCredentials)
      });
    }
    
    return res.success(credentials, `获取${account}账户凭据成功`);
  }
};

module.exports = {
  responseHandler,
  errorHandler,
  notFoundHandler,
  ErrorCodes,
  ErrorMessages,
  DevModeFeatures
};

/**
 * 数据验证中间件
 * 确保请求数据格式正确
 */

const Joi = require('joi');
const { RequestSchemas } = require('../models');

// 验证中间件工厂
function validate(schema, source = 'body') {
  return (req, res, next) => {
    const data = req[source];
    
    const { error, value } = schema.validate(data, {
      abortEarly: false, // 返回所有错误
      allowUnknown: false, // 不允许未知字段
      stripUnknown: true // 移除未知字段
    });
    
    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message.replace(/"/g, ''),
        value: detail.context?.value,
        type: detail.type
      }));
      
      return res.validationError(details);
    }
    
    // 将验证后的数据替换原始数据
    req[source] = value;
    next();
  };
}

// 分页参数验证
const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  sort: Joi.string().valid('createdAt', 'updatedAt', 'title', 'name', 'priority', 'status').default('createdAt'),
  order: Joi.string().valid('asc', 'desc').default('desc'),
  search: Joi.string().max(100).optional()
});

function validatePagination(req, res, next) {
  const { error, value } = paginationSchema.validate(req.query, {
    allowUnknown: true,
    stripUnknown: false
  });
  
  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message.replace(/"/g, ''),
      value: detail.context?.value
    }));
    
    return res.validationError(details);
  }
  
  req.pagination = value;
  next();
}

// ID参数验证
function validateId(paramName = 'id') {
  return (req, res, next) => {
    const id = req.params[paramName];
    
    if (!id || typeof id !== 'string' || id.length < 8) {
      return res.validationError([{
        field: paramName,
        message: 'Invalid ID format',
        value: id
      }]);
    }
    
    next();
  };
}

// 认证令牌验证
function validateAuthToken(req, res, next) {
  const token = req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    return res.unauthorized('认证令牌缺失');
  }
  
  if (typeof token !== 'string' || token.length < 10) {
    return res.unauthorized('认证令牌格式无效');
  }
  
  req.sessionToken = token;
  next();
}

// 文件上传验证
function validateFileUpload(options = {}) {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif'],
    required = false
  } = options;
  
  return (req, res, next) => {
    const file = req.file;
    
    if (!file && required) {
      return res.validationError([{
        field: 'file',
        message: 'File is required',
        value: null
      }]);
    }
    
    if (file) {
      if (file.size > maxSize) {
        return res.validationError([{
          field: 'file',
          message: `File size must be less than ${maxSize / 1024 / 1024}MB`,
          value: file.size
        }]);
      }
      
      if (!allowedTypes.includes(file.mimetype)) {
        return res.validationError([{
          field: 'file',
          message: `File type must be one of: ${allowedTypes.join(', ')}`,
          value: file.mimetype
        }]);
      }
    }
    
    next();
  };
}

// 日期范围验证
function validateDateRange(req, res, next) {
  const { startDate, endDate } = req.query;
  
  if (startDate && !moment(startDate).isValid()) {
    return res.validationError([{
      field: 'startDate',
      message: 'Invalid date format, use YYYY-MM-DD',
      value: startDate
    }]);
  }
  
  if (endDate && !moment(endDate).isValid()) {
    return res.validationError([{
      field: 'endDate',
      message: 'Invalid date format, use YYYY-MM-DD',
      value: endDate
    }]);
  }
  
  if (startDate && endDate && moment(startDate).isAfter(moment(endDate))) {
    return res.validationError([{
      field: 'dateRange',
      message: 'Start date must be before end date',
      value: { startDate, endDate }
    }]);
  }
  
  next();
}

// 预定义验证器
const Validators = {
  // 认证相关
  login: validate(RequestSchemas.Login),
  register: validate(RequestSchemas.Register),
  
  // 任务相关
  createTask: validate(RequestSchemas.CreateTask),
  updateTask: validate(RequestSchemas.UpdateTask),
  
  // 习惯相关
  createHabit: validate(RequestSchemas.CreateHabit),
  createHabitRecord: validate(RequestSchemas.CreateHabitRecord),
  
  // 通用验证
  pagination: validatePagination,
  id: validateId,
  authToken: validateAuthToken,
  dateRange: validateDateRange,
  
  // 文件上传
  avatar: validateFileUpload({
    maxSize: 2 * 1024 * 1024, // 2MB
    allowedTypes: ['image/jpeg', 'image/png'],
    required: false
  })
};

// 开发模式验证跳过
function devModeSkip(validator) {
  return (req, res, next) => {
    if (process.env.NODE_ENV === 'development' && req.query.skipValidation === 'true') {
      console.log('⚠️  开发模式：跳过验证');
      return next();
    }
    return validator(req, res, next);
  };
}

module.exports = {
  validate,
  validatePagination,
  validateId,
  validateAuthToken,
  validateFileUpload,
  validateDateRange,
  Validators,
  devModeSkip
};

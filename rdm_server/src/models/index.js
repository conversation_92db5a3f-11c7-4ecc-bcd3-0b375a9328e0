/**
 * TaskFlow 统一数据模型定义
 * 确保客户端和服务端数据格式一致
 */

const Joi = require('joi');
const moment = require('moment');

// 基础响应格式
const BaseResponse = {
  success: Joi.boolean().required(),
  message: Joi.string().optional(),
  timestamp: Joi.string().isoDate().required(),
  requestId: Joi.string().optional()
};

// 分页信息
const PaginationSchema = Joi.object({
  page: Joi.number().integer().min(1).required(),
  limit: Joi.number().integer().min(1).max(100).required(),
  total: Joi.number().integer().min(0).required(),
  pages: Joi.number().integer().min(0).required(),
  hasNext: Joi.boolean().required(),
  hasPrev: Joi.boolean().required()
});

// 用户模型
const UserSchema = Joi.object({
  id: Joi.string().required(),
  username: Joi.string().required(),
  email: Joi.string().email().required(),
  fullName: Joi.string().allow('', null).optional(),
  avatar: Joi.string().uri().allow('', null).optional(),
  isActive: Joi.boolean().default(true),
  isEmailVerified: Joi.boolean().default(false),
  lastLoginAt: Joi.string().isoDate().allow(null).optional(),
  createdAt: Joi.string().isoDate().required(),
  updatedAt: Joi.string().isoDate().required(),
  // 客户端不需要的敏感信息会被过滤
  sessionToken: Joi.string().optional() // 仅在登录响应中包含
});

// 任务模型
const TaskSchema = Joi.object({
  id: Joi.string().required(),
  title: Joi.string().required(),
  description: Joi.string().allow('', null).optional(),
  priority: Joi.string().valid('low', 'medium', 'high').required(),
  status: Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled').required(),
  category: Joi.string().allow('', null).optional(),
  tags: Joi.array().items(Joi.string()).default([]),
  isCompleted: Joi.boolean().required(),
  dueDate: Joi.string().isoDate().allow(null).optional(),
  completedAt: Joi.string().isoDate().allow(null).optional(),
  userId: Joi.string().required(),
  user: UserSchema.optional(), // 关联用户信息
  createdAt: Joi.string().isoDate().required(),
  updatedAt: Joi.string().isoDate().required()
});

// 习惯模型
const HabitSchema = Joi.object({
  id: Joi.string().required(),
  name: Joi.string().required(),
  description: Joi.string().allow('', null).optional(),
  type: Joi.string().valid('boolean', 'numeric', 'duration').required(),
  frequency: Joi.string().valid('daily', 'weekly', 'monthly').required(),
  targetValue: Joi.number().min(0).required(),
  unit: Joi.string().allow('', null).optional(),
  icon: Joi.string().allow('', null).optional(),
  color: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/).optional(),
  category: Joi.string().allow('', null).optional(),
  isActive: Joi.boolean().default(true),
  userId: Joi.string().required(),
  user: UserSchema.optional(),
  createdAt: Joi.string().isoDate().required(),
  updatedAt: Joi.string().isoDate().required()
});

// 习惯记录模型
const HabitRecordSchema = Joi.object({
  id: Joi.string().required(),
  habitId: Joi.string().required(),
  userId: Joi.string().required(),
  date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(), // YYYY-MM-DD
  value: Joi.number().min(0).required(),
  notes: Joi.string().allow('', null).optional(),
  habit: HabitSchema.optional(),
  user: UserSchema.optional(),
  createdAt: Joi.string().isoDate().required(),
  updatedAt: Joi.string().isoDate().required()
});

// API响应模型
const ApiResponseSchemas = {
  // 成功响应
  Success: Joi.object({
    ...BaseResponse,
    success: Joi.boolean().valid(true).required(),
    data: Joi.any().optional()
  }),

  // 错误响应
  Error: Joi.object({
    ...BaseResponse,
    success: Joi.boolean().valid(false).required(),
    error: Joi.object({
      code: Joi.string().required(),
      message: Joi.string().required(),
      details: Joi.any().optional()
    }).required()
  }),

  // 分页响应
  Paginated: Joi.object({
    ...BaseResponse,
    success: Joi.boolean().valid(true).required(),
    data: Joi.array().required(),
    pagination: PaginationSchema.required()
  }),

  // 登录响应
  Login: Joi.object({
    ...BaseResponse,
    success: Joi.boolean().valid(true).required(),
    data: Joi.object({
      user: UserSchema.required(),
      sessionToken: Joi.string().required(),
      expiresAt: Joi.string().isoDate().required()
    }).required()
  })
};

// 请求验证模型
const RequestSchemas = {
  // 登录请求
  Login: Joi.object({
    email: Joi.string().email().optional(),
    username: Joi.string().optional(),
    password: Joi.string().min(6).required()
  }).or('email', 'username'),

  // 注册请求
  Register: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
    fullName: Joi.string().max(100).optional()
  }),

  // 创建任务请求
  CreateTask: Joi.object({
    title: Joi.string().max(200).required(),
    description: Joi.string().max(1000).optional(),
    priority: Joi.string().valid('low', 'medium', 'high').default('medium'),
    category: Joi.string().max(50).optional(),
    tags: Joi.array().items(Joi.string().max(30)).max(10).default([]),
    dueDate: Joi.string().isoDate().optional()
  }),

  // 更新任务请求
  UpdateTask: Joi.object({
    title: Joi.string().max(200).optional(),
    description: Joi.string().max(1000).optional(),
    priority: Joi.string().valid('low', 'medium', 'high').optional(),
    status: Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled').optional(),
    category: Joi.string().max(50).optional(),
    tags: Joi.array().items(Joi.string().max(30)).max(10).optional(),
    dueDate: Joi.string().isoDate().allow(null).optional(),
    isCompleted: Joi.boolean().optional()
  }),

  // 创建习惯请求
  CreateHabit: Joi.object({
    name: Joi.string().max(100).required(),
    description: Joi.string().max(500).optional(),
    type: Joi.string().valid('boolean', 'numeric', 'duration').required(),
    frequency: Joi.string().valid('daily', 'weekly', 'monthly').default('daily'),
    targetValue: Joi.number().min(0).required(),
    unit: Joi.string().max(20).optional(),
    icon: Joi.string().max(10).optional(),
    color: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/).default('#4A90E2'),
    category: Joi.string().max(50).optional()
  }),

  // 创建习惯记录请求
  CreateHabitRecord: Joi.object({
    habitId: Joi.string().required(),
    date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).default(() => moment().format('YYYY-MM-DD')),
    value: Joi.number().min(0).required(),
    notes: Joi.string().max(200).optional()
  })
};

// 数据转换工具
const DataTransformers = {
  // Parse对象转换为标准格式
  parseObjectToStandard(parseObj, schema) {
    if (!parseObj) return null;

    const data = {
      id: parseObj.id,
      ...parseObj.attributes,
      createdAt: parseObj.get('createdAt')?.toISOString(),
      updatedAt: parseObj.get('updatedAt')?.toISOString()
    };

    // 移除敏感信息
    delete data.password;
    delete data.emailVerified;

    return data;
  },

  // 用户对象转换
  userToStandard(parseUser, includeSessionToken = false) {
    if (!parseUser) return null;

    const user = {
      id: parseUser.id,
      username: parseUser.get('username'),
      email: parseUser.get('email'),
      fullName: parseUser.get('fullName') || '',
      avatar: parseUser.get('avatar') || '',
      isActive: parseUser.get('isActive') !== false,
      isEmailVerified: parseUser.get('emailVerified') || false,
      lastLoginAt: parseUser.get('lastLoginAt')?.toISOString() || null,
      createdAt: parseUser.get('createdAt')?.toISOString(),
      updatedAt: parseUser.get('updatedAt')?.toISOString()
    };

    if (includeSessionToken) {
      user.sessionToken = parseUser.getSessionToken();
    }

    return user;
  },

  // 任务对象转换
  taskToStandard(parseTask) {
    if (!parseTask) return null;

    return {
      id: parseTask.id,
      title: parseTask.get('title'),
      description: parseTask.get('description') || '',
      priority: parseTask.get('priority'),
      status: parseTask.get('status'),
      category: parseTask.get('category') || '',
      tags: parseTask.get('tags') || [],
      isCompleted: parseTask.get('isCompleted') || false,
      dueDate: parseTask.get('dueDate')?.toISOString() || null,
      completedAt: parseTask.get('completedAt')?.toISOString() || null,
      userId: parseTask.get('userId'),
      user: parseTask.get('user') ? this.userToStandard(parseTask.get('user')) : null,
      createdAt: parseTask.get('createdAt')?.toISOString(),
      updatedAt: parseTask.get('updatedAt')?.toISOString()
    };
  },

  // 习惯对象转换
  habitToStandard(parseHabit) {
    if (!parseHabit) return null;

    return {
      id: parseHabit.id,
      name: parseHabit.get('name'),
      description: parseHabit.get('description') || '',
      type: parseHabit.get('type'),
      frequency: parseHabit.get('frequency'),
      targetValue: parseHabit.get('targetValue'),
      unit: parseHabit.get('unit') || '',
      icon: parseHabit.get('icon') || '',
      color: parseHabit.get('color') || '#4A90E2',
      category: parseHabit.get('category') || '',
      isActive: parseHabit.get('isActive') !== false,
      userId: parseHabit.get('userId'),
      user: parseHabit.get('user') ? this.userToStandard(parseHabit.get('user')) : null,
      createdAt: parseHabit.get('createdAt')?.toISOString(),
      updatedAt: parseHabit.get('updatedAt')?.toISOString()
    };
  }
};

module.exports = {
  UserSchema,
  TaskSchema,
  HabitSchema,
  HabitRecordSchema,
  PaginationSchema,
  ApiResponseSchemas,
  RequestSchemas,
  DataTransformers
};

# TaskFlow iOS 构建测试指南

## 🎯 测试目标
验证多环境配置是否正确工作，确保应用能在不同模式下连接到正确的后端服务器。

## ✅ 已解决的问题

### 1. Info.plist 冲突
- **问题**: Multiple commands produce Info.plist
- **解决**: 删除自定义Info.plist，使用Xcode自动生成
- **状态**: ✅ 已解决

### 2. 类型错误
- **问题**: ConfigurationManager类型不匹配
- **解决**: 添加专门的getServerConfigBool方法
- **状态**: ✅ 已解决

### 3. 网络安全设置
- **问题**: 复杂的NSAppTransportSecurity配置
- **解决**: 在代码中处理，Debug模式自动允许HTTP
- **状态**: ✅ 已解决

## 🔧 当前配置

### Debug模式
```
环境: Debug
后端: http://************:3000/api/v1
特点: 
- 允许HTTP连接
- 跳过证书验证
- 详细日志输出
- 自动连接测试
```

### Release模式
```
环境: Production/Development
后端: https://rdm.sanva.top/api/v1 (可配置)
特点:
- 强制HTTPS连接
- 标准证书验证
- 最小日志输出
```

## 🧪 测试步骤

### 1. 验证项目结构
```bash
cd rdm_client
xcodebuild -project rdm.xcodeproj -list
```

### 2. 检查语法
```bash
plutil -lint rdm.xcodeproj/project.pbxproj
```

### 3. 构建测试 (Debug)
```bash
xcodebuild -project rdm.xcodeproj -scheme rdm -configuration Debug build
```

### 4. 构建测试 (Release)
```bash
xcodebuild -project rdm.xcodeproj -scheme rdm -configuration Release build
```

## 📱 运行时测试

### 1. 启动应用
- Debug模式下会自动打印配置信息
- 查看控制台输出验证环境检测

### 2. 网络连接测试
- 应用会自动测试服务器连接
- 检查连接状态和响应

### 3. API调用测试
- 尝试登录或注册功能
- 验证API请求是否发送到正确的服务器

## 🔍 故障排除

### 构建失败
1. 清理项目: Product → Clean Build Folder
2. 重新构建: Product → Build
3. 检查错误日志

### 网络连接失败
1. 确认服务器运行状态
2. 检查IP地址和端口
3. 验证防火墙设置

### 配置不生效
1. 检查Configuration.plist是否在Bundle中
2. 验证环境检测逻辑
3. 查看控制台配置输出

## 📋 检查清单

- [ ] 项目可以正常构建 (Debug)
- [ ] 项目可以正常构建 (Release)
- [ ] Debug模式连接到本地服务器
- [ ] Release模式连接到开发服务器
- [ ] 网络安全设置正确
- [ ] 配置信息正确输出
- [ ] 服务器连接测试通过

## 🎉 完成状态

当前配置已经完成并解决了所有已知问题：
- ✅ 多环境自动切换
- ✅ 网络安全配置
- ✅ 构建错误修复
- ✅ 类型安全保证
- ✅ 自动化测试

现在可以正常构建和运行应用了！

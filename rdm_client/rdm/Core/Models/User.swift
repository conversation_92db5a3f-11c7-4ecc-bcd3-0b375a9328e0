//
//  User.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation

/// 用户模型
struct User: Codable, Identifiable, Equatable {
    let id: String
    let username: String
    let email: String
    let fullName: String?
    let isEmailVerified: Bool
    let authProvider: AuthProvider?
    let timezone: String?
    let preferences: UserPreferences
    let notificationSettings: NotificationSettings
    let lastLoginAt: Date?
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id = "objectId"
        case username
        case email
        case fullName
        case isEmailVerified
        case authProvider
        case timezone
        case preferences
        case notificationSettings
        case lastLoginAt
        case createdAt
        case updatedAt
    }
}

// MARK: - User Extensions

extension User {
    /// 显示名称
    var displayName: String {
        fullName?.isEmpty == false ? fullName! : username
    }
    
    /// 是否为新用户（注册不到24小时）
    var isNewUser: Bool {
        Date().timeIntervalSince(createdAt) < 24 * 60 * 60
    }
    
    /// 用户头像缩写
    var initials: String {
        let name = displayName
        let components = name.components(separatedBy: " ")
        
        if components.count >= 2 {
            let firstInitial = String(components[0].prefix(1))
            let lastInitial = String(components[1].prefix(1))
            return (firstInitial + lastInitial).uppercased()
        } else {
            return String(name.prefix(2)).uppercased()
        }
    }
}

// MARK: - Supporting Types

/// 认证提供商
enum AuthProvider: String, Codable, CaseIterable {
    case email = "email"
    case apple = "apple"
    
    var displayName: String {
        switch self {
        case .email:
            return "邮箱"
        case .apple:
            return "Apple ID"
        }
    }
    
    var iconName: String {
        switch self {
        case .email:
            return "envelope"
        case .apple:
            return "applelogo"
        }
    }
}

/// 用户偏好设置
struct UserPreferences: Codable, Equatable {
    let theme: String
    let notifications: Bool
    let weekStartsOn: String
    let timezone: String
    
    init(
        theme: String = "system",
        notifications: Bool = true,
        weekStartsOn: String = "monday",
        timezone: String = "UTC"
    ) {
        self.theme = theme
        self.notifications = notifications
        self.weekStartsOn = weekStartsOn
        self.timezone = timezone
    }
}

/// 通知设置
struct NotificationSettings: Codable, Equatable {
    let taskReminders: Bool
    let habitReminders: Bool
    let dailySummary: Bool
    let reminderTime: String
    
    init(
        taskReminders: Bool = true,
        habitReminders: Bool = true,
        dailySummary: Bool = false,
        reminderTime: String = "09:00"
    ) {
        self.taskReminders = taskReminders
        self.habitReminders = habitReminders
        self.dailySummary = dailySummary
        self.reminderTime = reminderTime
    }
}

// MARK: - User Creation

extension User {
    /// 创建默认用户（用于预览和测试）
    static func preview() -> User {
        User(
            id: "preview-user-id",
            username: "preview_user",
            email: "<EMAIL>",
            fullName: "预览用户",
            isEmailVerified: true,
            authProvider: .email,
            timezone: "Asia/Shanghai",
            preferences: UserPreferences(),
            notificationSettings: NotificationSettings(),
            lastLoginAt: Date(),
            createdAt: Date().addingTimeInterval(-7 * 24 * 60 * 60), // 7天前
            updatedAt: Date()
        )
    }
    
    /// 从API响应创建用户
    static func from(apiResponse: [String: Any]) -> User? {
        guard let data = try? JSONSerialization.data(withJSONObject: apiResponse),
              let user = try? JSONDecoder().decode(User.self, from: data) else {
            return nil
        }
        return user
    }
}

// MARK: - User Statistics

/// 用户统计信息
struct UserStatistics: Codable {
    let tasks: TaskStatistics
    let habits: HabitStatistics
    let account: AccountStatistics
    
    struct TaskStatistics: Codable {
        let total: Int
        let completed: Int
        let pending: Int
        let completionRate: String
        
        var completionRateValue: Double {
            Double(completionRate.replacingOccurrences(of: "%", with: "")) ?? 0.0
        }
    }
    
    struct HabitStatistics: Codable {
        let total: Int
        let active: Int
        let inactive: Int
        let totalRecords: Int
    }
    
    struct AccountStatistics: Codable {
        let memberSince: Date
        let lastLogin: Date?
        let isEmailVerified: Bool
    }
}

// MARK: - User Update Request
// Note: UserUpdateRequest is defined in TaskStatistics.swift to avoid duplication

/// 通知设置更新请求
struct NotificationUpdateRequest: Codable {
    let taskReminders: Bool?
    let habitReminders: Bool?
    let dailySummary: Bool?
    let reminderTime: String?
    
    init(
        taskReminders: Bool? = nil,
        habitReminders: Bool? = nil,
        dailySummary: Bool? = nil,
        reminderTime: String? = nil
    ) {
        self.taskReminders = taskReminders
        self.habitReminders = habitReminders
        self.dailySummary = dailySummary
        self.reminderTime = reminderTime
    }
}

/// 密码修改请求
struct ChangePasswordRequest: Codable {
    let currentPassword: String
    let newPassword: String
}

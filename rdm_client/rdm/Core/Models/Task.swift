//
//  Task.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation

/// 任务模型
struct TaskModel: Codable, Identifiable, Equatable {
    let id: String
    var title: String
    var description: String?
    var isCompleted: Bool
    var priority: TaskModel.Priority
    var status: TaskModel.Status
    var dueDate: Date?
    var category: String?
    var tags: [String]
    var estimatedDuration: TimeInterval?
    var actualDuration: TimeInterval?
    var completedAt: Date?
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id = "objectId"
        case title
        case description
        case isCompleted
        case priority
        case status
        case dueDate
        case category
        case tags
        case estimatedDuration
        case actualDuration
        case completedAt
        case createdAt
        case updatedAt
    }
}

// MARK: - Task Enums

extension TaskModel {
    /// 任务优先级
    enum Priority: String, Codable, CaseIterable {
        case high = "high"
        case medium = "medium"
        case low = "low"
        
        var displayName: String {
            switch self {
            case .high:
                return "高"
            case .medium:
                return "中"
            case .low:
                return "低"
            }
        }
        
        var color: String {
            switch self {
            case .high:
                return "red"
            case .medium:
                return "orange"
            case .low:
                return "gray"
            }
        }
        
        var iconName: String {
            switch self {
            case .high:
                return "exclamationmark.3"
            case .medium:
                return "exclamationmark.2"
            case .low:
                return "minus"
            }
        }
        
        var sortOrder: Int {
            switch self {
            case .high:
                return 3
            case .medium:
                return 2
            case .low:
                return 1
            }
        }
    }
    
    /// 任务状态
    enum Status: String, Codable, CaseIterable {
        case pending = "pending"
        case inProgress = "in_progress"
        case completed = "completed"
        case cancelled = "cancelled"
        
        var displayName: String {
            switch self {
            case .pending:
                return "待处理"
            case .inProgress:
                return "进行中"
            case .completed:
                return "已完成"
            case .cancelled:
                return "已取消"
            }
        }
        
        var iconName: String {
            switch self {
            case .pending:
                return "circle"
            case .inProgress:
                return "clock"
            case .completed:
                return "checkmark.circle.fill"
            case .cancelled:
                return "xmark.circle"
            }
        }
        
        var color: String {
            switch self {
            case .pending:
                return "gray"
            case .inProgress:
                return "blue"
            case .completed:
                return "green"
            case .cancelled:
                return "red"
            }
        }
    }
}

// MARK: - Task Extensions

extension TaskModel {
    /// 是否过期
    var isOverdue: Bool {
        guard let dueDate = self.dueDate, !self.isCompleted else { return false }
        return Date() > dueDate
    }
    
    /// 剩余时间
    var timeRemaining: TimeInterval? {
        guard let dueDate = self.dueDate, !self.isCompleted else { return nil }
        let remaining = dueDate.timeIntervalSinceNow
        return remaining > 0 ? remaining : nil
    }
    
    /// 剩余时间描述
    var timeRemainingDescription: String? {
        guard let timeRemaining = timeRemaining else { return nil }
        
        let days = Int(timeRemaining / (24 * 60 * 60))
        let hours = Int((timeRemaining.truncatingRemainder(dividingBy: 24 * 60 * 60)) / (60 * 60))
        
        if days > 0 {
            return "\(days)天"
        } else if hours > 0 {
            return "\(hours)小时"
        } else {
            return "不到1小时"
        }
    }
    
    /// 过期时间描述
    var overdueDescription: String? {
        guard isOverdue, let dueDate = self.dueDate else { return nil }
        
        let overdue = Date().timeIntervalSince(dueDate)
        let days = Int(overdue / (24 * 60 * 60))
        let hours = Int((overdue.truncatingRemainder(dividingBy: 24 * 60 * 60)) / (60 * 60))
        
        if days > 0 {
            return "过期\(days)天"
        } else if hours > 0 {
            return "过期\(hours)小时"
        } else {
            return "刚过期"
        }
    }
    
    /// 完成用时描述
    var completionDurationDescription: String? {
        guard let actualDuration = self.actualDuration else { return nil }
        
        let hours = Int(actualDuration / 3600)
        let minutes = Int((actualDuration.truncatingRemainder(dividingBy: 3600)) / 60)
        
        if hours > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
    
    /// 预估时间描述
    var estimatedDurationDescription: String? {
        guard let estimatedDuration = self.estimatedDuration else { return nil }
        
        let hours = Int(estimatedDuration / 3600)
        let minutes = Int((estimatedDuration.truncatingRemainder(dividingBy: 3600)) / 60)
        
        if hours > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
    
    /// 标签显示文本
    var tagsDisplayText: String {
        self.tags.isEmpty ? "" : self.tags.joined(separator: ", ")
    }
}

// MARK: - Task Creation

extension TaskModel {
    /// 创建新任务
    static func create(
        title: String,
        description: String? = nil,
        priority: Priority = .medium,
        dueDate: Date? = nil,
        category: String? = nil,
        tags: [String] = [],
        estimatedDuration: TimeInterval? = nil
    ) -> TaskModel {
        TaskModel(
            id: UUID().uuidString,
            title: title,
            description: description,
            isCompleted: false,
            priority: priority,
            status: .pending,
            dueDate: dueDate,
            category: category,
            tags: tags,
            estimatedDuration: estimatedDuration,
            actualDuration: nil,
            completedAt: nil,
            createdAt: Date(),
            updatedAt: Date()
        )
    }
    
    /// 预览任务（用于SwiftUI预览）
    static func preview() -> TaskModel {
        TaskModel(
            id: "preview-task-id",
            title: "完成项目文档",
            description: "编写技术文档和用户手册，包括API文档和部署指南",
            isCompleted: false,
            priority: .high,
            status: .inProgress,
            dueDate: Calendar.current.date(byAdding: .day, value: 2, to: Date()),
            category: "工作",
            tags: ["文档", "项目", "重要"],
            estimatedDuration: 4 * 3600, // 4小时
            actualDuration: nil,
            completedAt: nil,
            createdAt: Date().addingTimeInterval(-2 * 24 * 60 * 60), // 2天前
            updatedAt: Date()
        )
    }
    
    /// 已完成任务预览
    static func completedPreview() -> TaskModel {
        var task = preview()
        task.isCompleted = true
        task.status = .completed
        task.completedAt = Date().addingTimeInterval(-60 * 60) // 1小时前
        task.actualDuration = 3.5 * 3600 // 3.5小时
        return task
    }
}

// MARK: - Task Update Request
// Note: TaskUpdateRequest is defined in TaskStatistics.swift to avoid duplication

/// 批量任务操作请求
struct BatchTaskRequest: Codable {
    let taskIds: [String]
    let operation: Operation
    let data: TaskUpdateRequest?
    
    enum Operation: String, Codable {
        case complete = "complete"
        case incomplete = "incomplete"
        case delete = "delete"
        case update = "update"
    }
}

// MARK: - Task Statistics
// Note: TaskStatistics is defined in TaskStatistics.swift to avoid duplication

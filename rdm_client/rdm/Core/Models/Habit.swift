//
//  Habit.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation

/// 习惯模型
struct Habit: Codable, Identifiable, Equatable {
    let id: String
    var name: String
    var description: String?
    var frequency: Frequency
    var type: HabitType
    var targetValue: Int
    var unit: String?
    var icon: String?
    var color: String?
    var isActive: Bool
    var startDate: Date?
    var endDate: Date?
    var reminderTime: String?
    var isReminderEnabled: Bool
    var currentStreak: Int
    var bestStreak: Int
    var totalCompletions: Int
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id = "objectId"
        case name
        case description
        case frequency
        case type
        case targetValue
        case unit
        case icon
        case color
        case isActive
        case startDate
        case endDate
        case reminderTime
        case isReminderEnabled
        case currentStreak
        case bestStreak
        case totalCompletions
        case createdAt
        case updatedAt
    }
}

// MARK: - Habit Enums

extension Habit {
    /// 习惯频率
    enum Frequency: String, Codable, CaseIterable {
        case daily = "daily"
        case weekly = "weekly"
        case monthly = "monthly"
        case custom = "custom"
        
        var displayName: String {
            switch self {
            case .daily:
                return "每日"
            case .weekly:
                return "每周"
            case .monthly:
                return "每月"
            case .custom:
                return "自定义"
            }
        }
        
        var iconName: String {
            switch self {
            case .daily:
                return "sun.max"
            case .weekly:
                return "calendar.badge.clock"
            case .monthly:
                return "calendar"
            case .custom:
                return "gear"
            }
        }
    }
    
    /// 习惯类型
    enum HabitType: String, Codable, CaseIterable {
        case boolean = "boolean"
        case numeric = "numeric"
        case duration = "duration"
        
        var displayName: String {
            switch self {
            case .boolean:
                return "是/否"
            case .numeric:
                return "数值"
            case .duration:
                return "时长"
            }
        }
        
        var iconName: String {
            switch self {
            case .boolean:
                return "checkmark.circle"
            case .numeric:
                return "number"
            case .duration:
                return "clock"
            }
        }
    }
}

// MARK: - Habit Extensions

extension Habit {
    /// 今日是否已完成
    var isCompletedToday: Bool {
        // 这里需要结合 HabitRecord 来判断
        // 暂时返回 false，实际实现需要查询今日记录
        false
    }
    
    /// 连续天数描述
    var streakDescription: String {
        if currentStreak == 0 {
            return "尚未开始"
        } else if currentStreak == 1 {
            return "连续1天"
        } else {
            return "连续\(currentStreak)天"
        }
    }
    
    /// 最佳连续天数描述
    var bestStreakDescription: String {
        if bestStreak == 0 {
            return "暂无记录"
        } else if bestStreak == 1 {
            return "最佳1天"
        } else {
            return "最佳\(bestStreak)天"
        }
    }
    
    /// 完成次数描述
    var completionsDescription: String {
        if totalCompletions == 0 {
            return "尚未完成"
        } else {
            return "已完成\(totalCompletions)次"
        }
    }
    
    /// 目标值描述
    var targetDescription: String {
        let value = "\(targetValue)"
        if let unit = unit, !unit.isEmpty {
            return "\(value) \(unit)"
        } else {
            return value
        }
    }
    
    /// 提醒时间描述
    var reminderDescription: String? {
        guard isReminderEnabled, let reminderTime = reminderTime else { return nil }
        return "每日 \(reminderTime) 提醒"
    }
    
    /// 习惯状态描述
    var statusDescription: String {
        if !isActive {
            return "已暂停"
        } else if let endDate = endDate, Date() > endDate {
            return "已结束"
        } else {
            return "进行中"
        }
    }
    
    /// 是否可以记录（活跃且未结束）
    var canRecord: Bool {
        guard isActive else { return false }
        if let endDate = endDate {
            return Date() <= endDate
        }
        return true
    }
}

// MARK: - Habit Creation

extension Habit {
    /// 创建新习惯
    static func create(
        name: String,
        description: String? = nil,
        frequency: Frequency = .daily,
        type: HabitType = .boolean,
        targetValue: Int = 1,
        unit: String? = nil,
        icon: String? = nil,
        color: String? = nil,
        startDate: Date? = nil,
        endDate: Date? = nil,
        reminderTime: String? = nil,
        isReminderEnabled: Bool = false
    ) -> Habit {
        Habit(
            id: UUID().uuidString,
            name: name,
            description: description,
            frequency: frequency,
            type: type,
            targetValue: targetValue,
            unit: unit,
            icon: icon,
            color: color,
            isActive: true,
            startDate: startDate,
            endDate: endDate,
            reminderTime: reminderTime,
            isReminderEnabled: isReminderEnabled,
            currentStreak: 0,
            bestStreak: 0,
            totalCompletions: 0,
            createdAt: Date(),
            updatedAt: Date()
        )
    }
    
    /// 预览习惯（用于SwiftUI预览）
    static func preview() -> Habit {
        Habit(
            id: "preview-habit-id",
            name: "晨跑",
            description: "每天早上跑步30分钟，保持身体健康",
            frequency: .daily,
            type: .duration,
            targetValue: 30,
            unit: "分钟",
            icon: "figure.run",
            color: "#FF6B6B",
            isActive: true,
            startDate: Calendar.current.date(byAdding: .day, value: -30, to: Date()),
            endDate: nil,
            reminderTime: "07:00",
            isReminderEnabled: true,
            currentStreak: 5,
            bestStreak: 12,
            totalCompletions: 25,
            createdAt: Date().addingTimeInterval(-30 * 24 * 60 * 60), // 30天前
            updatedAt: Date()
        )
    }
    
    /// 阅读习惯预览
    static func readingPreview() -> Habit {
        Habit(
            id: "reading-habit-id",
            name: "阅读",
            description: "每天阅读至少30分钟",
            frequency: .daily,
            type: .duration,
            targetValue: 30,
            unit: "分钟",
            icon: "book",
            color: "#4ECDC4",
            isActive: true,
            startDate: Date(),
            endDate: nil,
            reminderTime: "21:00",
            isReminderEnabled: true,
            currentStreak: 3,
            bestStreak: 8,
            totalCompletions: 15,
            createdAt: Date().addingTimeInterval(-15 * 24 * 60 * 60), // 15天前
            updatedAt: Date()
        )
    }
}

// MARK: - Habit Record
// Note: HabitRecord and its extensions are defined in HabitRecord.swift to avoid duplication

// MARK: - Update Requests

// MARK: - Habit Update Request
// Note: HabitUpdateRequest is defined in TaskStatistics.swift to avoid duplication

// Note: HabitRecordRequest is defined in HabitRecord.swift to avoid duplication

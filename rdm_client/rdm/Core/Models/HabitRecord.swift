//
//  HabitRecord.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation

// MARK: - Habit Record

struct HabitRecord: Identifiable, Codable {
    let id: String
    let habitId: String
    let date: Date
    let value: Int
    let note: String?
    let duration: TimeInterval?
    let createdAt: Date
    let updatedAt: Date
    
    // MARK: - Computed Properties
    
    /// 格式化的值描述
    var valueDescription: String {
        if let duration = duration {
            let hours = Int(duration) / 3600
            let minutes = (Int(duration) % 3600) / 60
            
            if hours > 0 {
                return "\(hours)小时\(minutes)分钟"
            } else {
                return "\(minutes)分钟"
            }
        } else {
            return "\(value)"
        }
    }
    
    /// 是否是今天的记录
    var isToday: Bool {
        Calendar.current.isDateInToday(date)
    }
    
    /// 格式化的日期
    var formattedDate: String {
        let formatter = DateFormatter()
        if Calendar.current.isDateInToday(date) {
            return "今天"
        } else if Calendar.current.isDateInYesterday(date) {
            return "昨天"
        } else {
            formatter.dateFormat = "MM月dd日"
            return formatter.string(from: date)
        }
    }
    
    /// 格式化的时间
    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Extensions

extension HabitRecord {
    /// 创建预览数据
    static func preview() -> HabitRecord {
        HabitRecord(
            id: "preview-record-id",
            habitId: "preview-habit-id",
            date: Date(),
            value: 1,
            note: "完成了今天的习惯",
            duration: nil,
            createdAt: Date(),
            updatedAt: Date()
        )
    }
    
    /// 创建运动记录预览
    static func exercisePreview() -> HabitRecord {
        HabitRecord(
            id: "exercise-record-id",
            habitId: "exercise-habit-id",
            date: Date().addingTimeInterval(-3600), // 1小时前
            value: 30,
            note: "晨跑30分钟，感觉很好",
            duration: 30 * 60, // 30分钟
            createdAt: Date().addingTimeInterval(-3600),
            updatedAt: Date().addingTimeInterval(-3600)
        )
    }
}

// MARK: - Habit Record Request

/// 习惯记录请求
struct HabitRecordRequest: Codable {
    let date: Date?
    let value: Int?
    let note: String?
    let duration: TimeInterval?

    init(
        date: Date? = nil,
        value: Int? = nil,
        note: String? = nil,
        duration: TimeInterval? = nil
    ) {
        self.date = date
        self.value = value
        self.note = note
        self.duration = duration
    }
}

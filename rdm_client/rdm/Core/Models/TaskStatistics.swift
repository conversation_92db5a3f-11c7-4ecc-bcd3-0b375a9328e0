//
//  TaskStatistics.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation

// MARK: - Task Statistics

struct TaskStatistics: Codable {
    let total: Int
    let completed: Int
    let pending: Int
    let overdue: Int
    let completionRateValue: Double
    let byPriority: PriorityStatistics
    let byStatus: StatusStatistics
    let period: String
    let startDate: Date
    let endDate: Date
    
    enum CodingKeys: String, CodingKey {
        case total
        case completed
        case pending
        case overdue
        case completionRateValue = "completionRate"
        case byPriority
        case byStatus
        case period
        case startDate
        case endDate
    }
}

// MARK: - Priority Statistics

struct PriorityStatistics: Codable {
    let high: Int
    let medium: Int
    let low: Int
}

// MARK: - Status Statistics

struct StatusStatistics: Codable {
    let todo: Int
    let inProgress: Int
    let completed: Int
    let cancelled: Int
}

// MARK: - Task Update Request

struct TaskUpdateRequest: Codable {
    let title: String?
    let description: String?
    let priority: TaskModel.Priority?
    let status: TaskModel.Status?
    let dueDate: Date?
    let category: String?
    let tags: [String]?
    let estimatedDuration: TimeInterval?
    
    init(
        title: String? = nil,
        description: String? = nil,
        priority: TaskModel.Priority? = nil,
        status: TaskModel.Status? = nil,
        dueDate: Date? = nil,
        category: String? = nil,
        tags: [String]? = nil,
        estimatedDuration: TimeInterval? = nil
    ) {
        self.title = title
        self.description = description
        self.priority = priority
        self.status = status
        self.dueDate = dueDate
        self.category = category
        self.tags = tags
        self.estimatedDuration = estimatedDuration
    }
}

// MARK: - User Update Request

struct UserUpdateRequest: Codable {
    let username: String?
    let email: String?
    let fullName: String?
    let timezone: String?

    init(
        username: String? = nil,
        email: String? = nil,
        fullName: String? = nil,
        timezone: String? = nil
    ) {
        self.username = username
        self.email = email
        self.fullName = fullName
        self.timezone = timezone
    }
}

// MARK: - Habit Update Request

struct HabitUpdateRequest: Codable {
    let name: String?
    let description: String?
    let frequency: Habit.Frequency?
    let type: Habit.HabitType?
    let targetValue: Int?
    let unit: String?
    let icon: String?
    let color: String?
    let isActive: Bool?
    let startDate: Date?
    let endDate: Date?
    let reminderTime: String?
    let isReminderEnabled: Bool?

    init(
        name: String? = nil,
        description: String? = nil,
        frequency: Habit.Frequency? = nil,
        type: Habit.HabitType? = nil,
        targetValue: Int? = nil,
        unit: String? = nil,
        icon: String? = nil,
        color: String? = nil,
        isActive: Bool? = nil,
        startDate: Date? = nil,
        endDate: Date? = nil,
        reminderTime: String? = nil,
        isReminderEnabled: Bool? = nil
    ) {
        self.name = name
        self.description = description
        self.frequency = frequency
        self.type = type
        self.targetValue = targetValue
        self.unit = unit
        self.icon = icon
        self.color = color
        self.isActive = isActive
        self.startDate = startDate
        self.endDate = endDate
        self.reminderTime = reminderTime
        self.isReminderEnabled = isReminderEnabled
    }
}

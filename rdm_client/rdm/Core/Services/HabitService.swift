//
//  HabitService.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation
import Combine

/// 习惯服务
@MainActor
class HabitService: ObservableObject {
    static let shared = HabitService()
    
    // MARK: - Published Properties
    
    @Published var habits: [Habit] = []
    @Published var habitRecords: [String: [HabitRecord]] = [:] // habitId -> records
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    
    private let apiClient = APIClient.shared
    private let cacheManager = CacheManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Cache Keys
    
    private enum CacheKeys {
        static let habits = "cached_habits"
        static let habitRecords = "cached_habit_records"
    }
    
    // MARK: - Initialization
    
    private init() {
        setupBindings()
        loadCachedData()
    }
    
    // MARK: - Public Methods
    
    /// 获取习惯列表
    func fetchHabits(
        page: Int = 1,
        limit: Int = 20,
        frequency: Habit.Frequency? = nil,
        type: Habit.HabitType? = nil,
        isActive: Bool? = nil,
        sortBy: String? = nil,
        sortOrder: String? = nil
    ) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let response = try await apiClient.getHabits(
                page: page,
                limit: limit,
                frequency: frequency?.rawValue,
                isActive: isActive
            )
            
            if page == 1 {
                habits = response.habits
            } else {
                habits.append(contentsOf: response.habits)
            }
            
            // 缓存习惯数据
            cacheHabits()
            
        } catch {
            errorMessage = error.localizedDescription
            
            // 如果是第一页且网络错误，使用缓存数据
            if page == 1 && !NetworkMonitor.shared.status.isConnected {
                loadCachedData()
            }
        }
        
        isLoading = false
    }
    
    /// 创建习惯
    func createHabit(
        name: String,
        description: String? = nil,
        frequency: Habit.Frequency = .daily,
        type: Habit.HabitType = .boolean,
        targetValue: Int = 1,
        unit: String? = nil,
        icon: String? = nil,
        color: String? = nil,
        startDate: Date? = nil,
        endDate: Date? = nil,
        reminderTime: String? = nil,
        isReminderEnabled: Bool = false
    ) async throws -> Habit {
        let request = HabitCreateRequest(
            name: name,
            description: description,
            frequency: frequency,
            type: type,
            targetValue: targetValue,
            unit: unit,
            icon: icon,
            color: color,
            startDate: startDate,
            endDate: endDate,
            reminderTime: reminderTime,
            isReminderEnabled: isReminderEnabled
        )
        
        let habit = try await apiClient.createHabit(request)
        
        // 添加到本地列表
        habits.insert(habit, at: 0)
        
        // 更新缓存
        cacheHabits()
        
        return habit
    }
    
    /// 更新习惯
    func updateHabit(_ habit: Habit, with updateRequest: HabitUpdateRequest) async throws -> Habit {
        let updatedHabit = try await apiClient.updateHabit(habit.id, updateRequest)
        
        // 更新本地列表
        if let index = habits.firstIndex(where: { $0.id == habit.id }) {
            habits[index] = updatedHabit
        }
        
        // 更新缓存
        cacheHabits()
        
        return updatedHabit
    }
    
    /// 切换习惯激活状态
    func toggleHabitActive(_ habit: Habit) async throws -> Habit {
        let updateRequest = HabitUpdateRequest(isActive: !habit.isActive)
        return try await updateHabit(habit, with: updateRequest)
    }

    /// 删除习惯
    func deleteHabit(_ habit: Habit) async throws {
        try await apiClient.deleteHabit(habit.id)

        // 从本地列表移除
        habits.removeAll { $0.id == habit.id }

        // 移除相关记录
        habitRecords.removeValue(forKey: habit.id)

        // 更新缓存
        cacheHabits()
        cacheHabitRecords()
    }
    
    /// 记录习惯完成
    func recordHabitCompletion(
        habitId: String,
        date: Date = Date(),
        value: Int = 1,
        note: String? = nil,
        duration: TimeInterval? = nil
    ) async throws -> HabitRecord {
        let request = HabitRecordRequest(
            date: date,
            value: value,
            note: note,
            duration: duration
        )
        
        let record = try await apiClient.recordHabitCompletion(habitId: habitId, request: request)
        
        // 添加到本地记录
        if habitRecords[habitId] == nil {
            habitRecords[habitId] = []
        }
        
        // 检查是否已有当天记录，如果有则替换，否则添加
        let dateKey = Calendar.current.startOfDay(for: date)
        if let existingIndex = habitRecords[habitId]?.firstIndex(where: {
            Calendar.current.isDate($0.date, inSameDayAs: dateKey)
        }) {
            habitRecords[habitId]?[existingIndex] = record
        } else {
            habitRecords[habitId]?.append(record)
        }
        
        // 更新习惯统计
        await updateHabitStatistics(habitId: habitId)
        
        // 更新缓存
        cacheHabitRecords()
        
        return record
    }

    /// 记录习惯完成（支持Double值和notes参数）
    func recordHabitCompletion(
        habitId: String,
        value: Double,
        date: Date = Date(),
        notes: String? = nil
    ) async throws -> HabitRecord {
        return try await recordHabitCompletion(
            habitId: habitId,
            date: date,
            value: Int(value),
            note: notes,
            duration: nil
        )
    }
    
    /// 获取习惯记录
    func getHabitRecords(habitId: String, startDate: Date? = nil, endDate: Date? = nil) async throws -> [HabitRecord] {
        // 先检查本地缓存
        if let cachedRecords = habitRecords[habitId] {
            var filteredRecords = cachedRecords
            
            if let startDate = startDate {
                filteredRecords = filteredRecords.filter { $0.date >= startDate }
            }
            
            if let endDate = endDate {
                filteredRecords = filteredRecords.filter { $0.date <= endDate }
            }
            
            // 如果有网络连接，异步更新数据
            if NetworkMonitor.shared.status.isConnected {
                Task {
                    try? await fetchHabitRecordsFromServer(habitId: habitId)
                }
            }
            
            return filteredRecords.sorted { $0.date > $1.date }
        }
        
        // 如果本地没有缓存，从服务器获取
        return try await fetchHabitRecordsFromServer(habitId: habitId)
    }
    
    /// 检查习惯今日是否已完成
    func isHabitCompletedToday(_ habitId: String) -> Bool {
        guard let records = habitRecords[habitId] else { return false }
        
        let today = Calendar.current.startOfDay(for: Date())
        return records.contains { Calendar.current.isDate($0.date, inSameDayAs: today) }
    }
    
    /// 获取习惯连续天数
    func getHabitStreak(_ habitId: String) -> Int {
        guard let records = habitRecords[habitId] else { return 0 }
        
        let sortedRecords = records.sorted { $0.date > $1.date }
        let today = Calendar.current.startOfDay(for: Date())
        
        var streak = 0
        var currentDate = today
        
        for record in sortedRecords {
            let recordDate = Calendar.current.startOfDay(for: record.date)
            
            if Calendar.current.isDate(recordDate, inSameDayAs: currentDate) {
                streak += 1
                currentDate = Calendar.current.date(byAdding: .day, value: -1, to: currentDate)!
            } else {
                break
            }
        }
        
        return streak
    }
    
    // Note: toggleHabitActive is already defined above, removing duplicate
    
    /// 刷新习惯列表
    func refreshHabits() async {
        await fetchHabits()
    }
    
    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // 监听网络状态变化
        NetworkMonitor.shared.$status
            .filter { $0.isConnected }
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.syncWithServer()
                }
            }
            .store(in: &cancellables)
    }
    
    private func loadCachedData() {
        // 加载缓存的习惯
        if let cachedData = cacheManager.getData(forKey: CacheKeys.habits),
           let cachedHabits = try? JSONDecoder().decode([Habit].self, from: cachedData) {
            habits = cachedHabits
        }
        
        // 加载缓存的习惯记录
        if let cachedData = cacheManager.getData(forKey: CacheKeys.habitRecords),
           let cachedRecords = try? JSONDecoder().decode([String: [HabitRecord]].self, from: cachedData) {
            habitRecords = cachedRecords
        }
    }
    
    private func cacheHabits() {
        if let data = try? JSONEncoder().encode(habits) {
            cacheManager.setData(data, forKey: CacheKeys.habits)
        }
    }
    
    private func cacheHabitRecords() {
        if let data = try? JSONEncoder().encode(habitRecords) {
            cacheManager.setData(data, forKey: CacheKeys.habitRecords)
        }
    }
    
    private func syncWithServer() async {
        // 如果有网络连接，同步数据
        if NetworkMonitor.shared.status.isConnected {
            await fetchHabits()
            
            // 同步所有习惯的记录
            for habit in habits {
                try? await fetchHabitRecordsFromServer(habitId: habit.id)
            }
        }
    }
    
    private func fetchHabitRecordsFromServer(habitId: String) async throws -> [HabitRecord] {
        // 这里应该调用API获取习惯记录
        // 暂时返回空数组，实际实现需要添加相应的API端点
        let records: [HabitRecord] = []
        
        habitRecords[habitId] = records
        cacheHabitRecords()
        
        return records
    }
    
    private func updateHabitStatistics(habitId: String) async {
        // 更新习惯的统计信息（连续天数、总完成次数等）
        guard let habitIndex = habits.firstIndex(where: { $0.id == habitId }) else { return }
        
        let currentStreak = getHabitStreak(habitId)
        let totalCompletions = habitRecords[habitId]?.count ?? 0
        
        var updatedHabit = habits[habitIndex]
        updatedHabit.currentStreak = currentStreak
        updatedHabit.totalCompletions = totalCompletions
        
        if currentStreak > updatedHabit.bestStreak {
            updatedHabit.bestStreak = currentStreak
        }
        
        habits[habitIndex] = updatedHabit
        cacheHabits()
    }
}

// MARK: - Habit Service Extensions

extension HabitService {
    /// 获取活跃习惯
    var activeHabits: [Habit] {
        return habits.filter { $0.isActive }
    }
    
    /// 获取今日需要完成的习惯
    var todayHabits: [Habit] {
        return activeHabits.filter { habit in
            switch habit.frequency {
            case .daily:
                return true
            case .weekly:
                // 检查是否在本周需要完成
                return true // 简化实现
            case .monthly:
                // 检查是否在本月需要完成
                return true // 简化实现
            case .custom:
                return true // 简化实现
            }
        }
    }
    
    /// 获取今日已完成的习惯数量
    var todayCompletedCount: Int {
        return todayHabits.filter { isHabitCompletedToday($0.id) }.count
    }
    
    /// 今日完成率
    var todayCompletionRate: Double {
        guard !todayHabits.isEmpty else { return 0.0 }
        return Double(todayCompletedCount) / Double(todayHabits.count)
    }
}

// MARK: - Supporting Types

/// 习惯创建请求
struct HabitCreateRequest: Codable {
    let name: String
    let description: String?
    let frequency: Habit.Frequency
    let type: Habit.HabitType
    let targetValue: Int
    let unit: String?
    let icon: String?
    let color: String?
    let startDate: Date?
    let endDate: Date?
    let reminderTime: String?
    let isReminderEnabled: Bool
}

/// 习惯列表响应
struct HabitListResponse: Codable {
    let habits: [Habit]
    let pagination: Pagination
    
    struct Pagination: Codable {
        let page: Int
        let limit: Int
        let total: Int
        let totalPages: Int
    }
}

// MARK: - Habit Record Request
// Note: HabitRecordRequest is defined in HabitRecord.swift to avoid duplication

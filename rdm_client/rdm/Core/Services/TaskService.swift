//
//  TaskService.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation
import Combine

/// 任务服务
@MainActor
class TaskService: ObservableObject {
    static let shared = TaskService()
    
    // MARK: - Published Properties
    
    @Published var tasks: [TaskModel] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    
    private let apiClient = APIClient.shared
    private let cacheManager = CacheManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Cache Keys
    
    private enum CacheKeys {
        static let tasks = "cached_tasks"
        static let lastSyncTime = "tasks_last_sync_time"
    }
    
    // MARK: - Initialization
    
    private init() {
        setupBindings()
        loadCachedTasks()
    }
    
    // MARK: - Public Methods
    
    /// 获取任务列表
    func fetchTasks(
        page: Int = 1,
        limit: Int = 20,
        status: TaskModel.Status? = nil,
        priority: TaskModel.Priority? = nil,
        isCompleted: Bool? = nil,
        category: String? = nil,
        search: String? = nil,
        sortBy: String = "createdAt",
        sortOrder: String = "desc"
    ) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let response = try await apiClient.getTasks(
                page: page,
                limit: limit,
                status: status?.rawValue,
                priority: priority?.rawValue,
                isCompleted: isCompleted,
                category: category,
                search: search,
                sortBy: sortBy,
                sortOrder: sortOrder
            )
            
            if page == 1 {
                tasks = response.tasks
            } else {
                tasks.append(contentsOf: response.tasks)
            }
            
            // 缓存任务数据
            cacheTasks()
            
        } catch {
            errorMessage = error.localizedDescription
            
            // 如果是第一页且网络错误，使用缓存数据
            if page == 1 && !NetworkMonitor.shared.status.isConnected {
                loadCachedTasks()
            }
        }
        
        isLoading = false
    }
    
    /// 创建任务
    func createTask(
        title: String,
        description: String? = nil,
        priority: TaskModel.Priority = .medium,
        dueDate: Date? = nil,
        category: String? = nil,
        tags: [String] = [],
        estimatedDuration: TimeInterval? = nil
    ) async throws -> TaskModel {
        let request = TaskCreateRequest(
            title: title,
            description: description,
            priority: priority,
            dueDate: dueDate,
            category: category,
            tags: tags,
            estimatedDuration: estimatedDuration
        )
        
        let task = try await apiClient.createTask(request)
        
        // 添加到本地列表
        tasks.insert(task, at: 0)
        
        // 更新缓存
        cacheTasks()
        
        return task
    }
    
    /// 更新任务
    func updateTask(_ task: TaskModel, with updateRequest: TaskUpdateRequest) async throws -> TaskModel {
        let updatedTask = try await apiClient.updateTask(task.id, updateRequest)
        
        // 更新本地列表
        if let index = tasks.firstIndex(where: { $0.id == task.id }) {
            tasks[index] = updatedTask
        }
        
        // 更新缓存
        cacheTasks()
        
        return updatedTask
    }
    
    /// 删除任务
    func deleteTask(_ task: TaskModel) async throws {
        try await apiClient.deleteTask(task.id)
        
        // 从本地列表移除
        tasks.removeAll { $0.id == task.id }
        
        // 更新缓存
        cacheTasks()
    }
    
    /// 标记任务完成/未完成
    func toggleTaskCompletion(_ task: TaskModel) async throws -> TaskModel {
        let updateRequest = TaskUpdateRequest(
            status: !task.isCompleted ? .completed : .pending
        )
        
        return try await updateTask(task, with: updateRequest)
    }
    
    /// 批量操作任务
    func batchUpdateTasks(
        taskIds: [String],
        operation: BatchTaskRequest.Operation,
        data: TaskUpdateRequest? = nil
    ) async throws {
        let request = BatchTaskRequest(
            taskIds: taskIds,
            operation: operation,
            data: data
        )
        
        try await apiClient.batchUpdateTasks(request)
        
        // 重新获取任务列表
        await fetchTasks()
    }
    
    /// 获取任务统计
    func getTaskStatistics(period: String = "week") async throws -> TaskStatistics {
        return try await apiClient.getTaskStatistics(period: period)
    }

    /// 按完成状态筛选任务
    func filterTasksByCompletion(_ isCompleted: Bool) async {
        await fetchTasks(isCompleted: isCompleted)
    }

    /// 按优先级筛选任务
    func filterTasksByPriority(_ priority: TaskModel.Priority) async {
        await fetchTasks(priority: priority)
    }
    
    /// 搜索任务
    func searchTasks(_ query: String) async {
        await fetchTasks(search: query)
    }
    
    /// 按分类筛选任务
    func filterTasksByCategory(_ category: String?) async {
        await fetchTasks(category: category)
    }
    
    /// 按优先级筛选任务
    func filterTasksByPriority(_ priority: TaskModel.Priority?) async {
        await fetchTasks(priority: priority)
    }
    
    /// 按完成状态筛选任务
    func filterTasksByCompletion(_ isCompleted: Bool?) async {
        await fetchTasks(isCompleted: isCompleted)
    }
    
    /// 刷新任务列表
    func refreshTasks() async {
        await fetchTasks()
    }
    
    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // 监听网络状态变化
        NetworkMonitor.shared.$status
            .filter { $0.isConnected }
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.syncWithServer()
                }
            }
            .store(in: &cancellables)
    }
    
    private func loadCachedTasks() {
        if let cachedData = cacheManager.getData(forKey: CacheKeys.tasks),
           let cachedTasks = try? JSONDecoder().decode([TaskModel].self, from: cachedData) {
            tasks = cachedTasks
        }
    }
    
    private func cacheTasks() {
        if let data = try? JSONEncoder().encode(tasks) {
            cacheManager.setData(data, forKey: CacheKeys.tasks)
        }
    }
    
    private func syncWithServer() async {
        // 如果有网络连接，同步数据
        if NetworkMonitor.shared.status.isConnected {
            await fetchTasks()
        }
    }
}

// MARK: - Task Service Extensions

extension TaskService {
    /// 获取今日任务
    var todayTasks: [TaskModel] {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        
        return tasks.filter { task in
            guard let dueDate = task.dueDate else { return false }
            return dueDate >= today && dueDate < tomorrow
        }
    }
    
    /// 获取过期任务
    var overdueTasks: [TaskModel] {
        return tasks.filter { $0.isOverdue }
    }
    
    /// 获取高优先级任务
    var highPriorityTasks: [TaskModel] {
        return tasks.filter { $0.priority == .high && !$0.isCompleted }
    }
    
    /// 获取已完成任务
    var completedTasks: [TaskModel] {
        return tasks.filter { $0.isCompleted }
    }
    
    /// 获取未完成任务
    var pendingTasks: [TaskModel] {
        return tasks.filter { !$0.isCompleted }
    }
    
    /// 获取所有分类
    var categories: [String] {
        let allCategories = tasks.compactMap { $0.category }
        return Array(Set(allCategories)).sorted()
    }
    
    /// 获取所有标签
    var allTags: [String] {
        let allTags = tasks.flatMap { $0.tags }
        return Array(Set(allTags)).sorted()
    }
    
    /// 完成率
    var completionRate: Double {
        guard !tasks.isEmpty else { return 0.0 }
        let completedCount = tasks.filter { $0.isCompleted }.count
        return Double(completedCount) / Double(tasks.count)
    }
}

// MARK: - Supporting Types

/// 任务创建请求
struct TaskCreateRequest: Codable {
    let title: String
    let description: String?
    let priority: TaskModel.Priority
    let dueDate: Date?
    let category: String?
    let tags: [String]
    let estimatedDuration: TimeInterval?
}

/// 任务列表响应
struct TaskListResponse: Codable {
    let tasks: [TaskModel]
    let pagination: Pagination
    
    struct Pagination: Codable {
        let page: Int
        let limit: Int
        let total: Int
        let totalPages: Int
    }
}

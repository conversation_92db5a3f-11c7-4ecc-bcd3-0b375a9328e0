//
//  AuthService.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation
import Combine
import AuthenticationServices

/// 认证服务
@MainActor
class AuthService: ObservableObject {
    static let shared = AuthService()
    
    // MARK: - Published Properties
    
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var isLoading = false
    
    // MARK: - Private Properties
    
    private let apiClient = APIClient.shared
    private let keychain = KeychainManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Constants
    
    private enum Keys {
        static let authToken = "auth_token"
        static let userId = "user_id"
        static let userEmail = "user_email"
    }
    
    // MARK: - Initialization
    
    private init() {
        setupBindings()
    }
    
    // MARK: - Public Methods
    
    /// 检查认证状态
    func checkAuthenticationStatus() async {
        guard let token = keychain.get(Keys.authToken),
              let userId = keychain.get(Keys.userId) else {
            isAuthenticated = false
            currentUser = nil
            return
        }
        
        do {
            // 验证token有效性并获取用户信息
            apiClient.setAuthToken(token)
            let user = try await apiClient.getCurrentUser()
            
            currentUser = user
            isAuthenticated = true
        } catch {
            // Token无效，清除本地数据
            do {
                try await signOut()
            } catch {
                // Handle sign out error silently
            }
        }
    }
    
    /// 用户注册
    func signUp(email: String, password: String, username: String) async throws -> User {
        isLoading = true
        defer { isLoading = false }
        
        let request = SignUpRequest(
            email: email,
            password: password,
            username: username
        )
        
        let response = try await apiClient.signUp(request)
        
        // 保存认证信息
        keychain.set(response.sessionToken, forKey: Keys.authToken)
        keychain.set(response.user.id, forKey: Keys.userId)
        keychain.set(response.user.email, forKey: Keys.userEmail)
        
        // 设置API客户端token
        apiClient.setAuthToken(response.sessionToken)
        
        // 更新状态
        currentUser = response.user
        isAuthenticated = true
        
        return response.user
    }
    
    /// 用户登录
    func signIn(email: String, password: String) async throws -> User {
        isLoading = true
        defer { isLoading = false }
        
        let request = SignInRequest(
            email: email,
            password: password
        )
        
        let response = try await apiClient.signIn(request)
        
        // 保存认证信息
        keychain.set(response.sessionToken, forKey: Keys.authToken)
        keychain.set(response.user.id, forKey: Keys.userId)
        keychain.set(response.user.email, forKey: Keys.userEmail)
        
        // 设置API客户端token
        apiClient.setAuthToken(response.sessionToken)
        
        // 更新状态
        currentUser = response.user
        isAuthenticated = true
        
        return response.user
    }
    
    /// Apple ID 登录
    func signInWithApple() async throws -> User {
        isLoading = true
        defer { isLoading = false }
        
        // 请求Apple ID授权
        let appleIDCredential = try await requestAppleIDAuthorization()
        
        let request = AppleSignInRequest(
            identityToken: appleIDCredential.identityToken,
            authorizationCode: appleIDCredential.authorizationCode,
            user: appleIDCredential.user
        )
        
        let response = try await apiClient.signInWithApple(request)
        
        // 保存认证信息
        keychain.set(response.sessionToken, forKey: Keys.authToken)
        keychain.set(response.user.id, forKey: Keys.userId)
        keychain.set(response.user.email, forKey: Keys.userEmail)
        
        // 设置API客户端token
        apiClient.setAuthToken(response.sessionToken)
        
        // 更新状态
        currentUser = response.user
        isAuthenticated = true
        
        return response.user
    }
    
    /// 用户登出
    func signOut() async throws {
        isLoading = true
        defer { isLoading = false }
        
        // 调用服务器登出API
        if isAuthenticated {
            try? await apiClient.signOut()
        }
        
        // 清除本地数据
        keychain.delete(Keys.authToken)
        keychain.delete(Keys.userId)
        keychain.delete(Keys.userEmail)
        
        // 清除API客户端token
        apiClient.clearAuthToken()
        
        // 更新状态
        currentUser = nil
        isAuthenticated = false
    }
    
    /// 忘记密码
    func forgotPassword(email: String) async throws {
        let request = ForgotPasswordRequest(email: email)
        try await apiClient.forgotPassword(request)
    }
    
    /// 更新用户信息
    func updateUser(_ updateRequest: UserUpdateRequest) async throws -> User {
        guard isAuthenticated else {
            throw AuthError.notAuthenticated
        }
        
        let updatedUser = try await apiClient.updateUser(updateRequest)
        currentUser = updatedUser
        
        return updatedUser
    }
    
    /// 修改密码
    func changePassword(currentPassword: String, newPassword: String) async throws {
        guard isAuthenticated else {
            throw AuthError.notAuthenticated
        }
        
        let request = ChangePasswordRequest(
            currentPassword: currentPassword,
            newPassword: newPassword
        )
        
        try await apiClient.changePassword(request)
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // 监听网络状态变化，在网络恢复时重新验证认证状态
        NetworkMonitor.shared.$status
            .filter { $0.isConnected }
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.checkAuthenticationStatus()
                }
            }
            .store(in: &cancellables)
    }
    
    private func requestAppleIDAuthorization() async throws -> AppleIDCredential {
        return try await withCheckedThrowingContinuation { continuation in
            let request = ASAuthorizationAppleIDProvider().createRequest()
            request.requestedScopes = [.fullName, .email]
            
            let authorizationController = ASAuthorizationController(authorizationRequests: [request])
            
            let delegate = AppleSignInDelegate { result in
                continuation.resume(with: result)
            }
            
            authorizationController.delegate = delegate
            authorizationController.presentationContextProvider = delegate
            authorizationController.performRequests()
        }
    }
}

// MARK: - Supporting Types

/// 认证错误
enum AuthError: LocalizedError {
    case notAuthenticated
    case invalidCredentials
    case networkError
    case appleSignInFailed
    case tokenExpired
    
    var errorDescription: String? {
        switch self {
        case .notAuthenticated:
            return "用户未登录"
        case .invalidCredentials:
            return "邮箱或密码错误"
        case .networkError:
            return "网络连接失败"
        case .appleSignInFailed:
            return "Apple ID 登录失败"
        case .tokenExpired:
            return "登录已过期，请重新登录"
        }
    }
}

/// 注册请求
struct SignUpRequest: Codable {
    let email: String
    let password: String
    let username: String
}

/// 登录请求
struct SignInRequest: Codable {
    let email: String
    let password: String
}

/// Apple ID 登录请求
struct AppleSignInRequest: Codable {
    let identityToken: String
    let authorizationCode: String
    let user: AppleUser?
    
    struct AppleUser: Codable {
        let name: PersonName?
        let email: String?
        
        struct PersonName: Codable {
            let firstName: String?
            let lastName: String?
        }
    }
}

/// 忘记密码请求
struct ForgotPasswordRequest: Codable {
    let email: String
}

/// 认证响应
struct AuthResponse: Codable {
    let user: User
    let sessionToken: String
}

/// Apple ID 凭证
struct AppleIDCredential {
    let identityToken: String
    let authorizationCode: String
    let user: AppleSignInRequest.AppleUser?
}

// MARK: - Apple Sign In Delegate

private class AppleSignInDelegate: NSObject, ASAuthorizationControllerDelegate, ASAuthorizationControllerPresentationContextProviding {
    private let completion: (Result<AppleIDCredential, Error>) -> Void
    
    init(completion: @escaping (Result<AppleIDCredential, Error>) -> Void) {
        self.completion = completion
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        guard let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential,
              let identityTokenData = appleIDCredential.identityToken,
              let identityToken = String(data: identityTokenData, encoding: .utf8),
              let authorizationCodeData = appleIDCredential.authorizationCode,
              let authorizationCode = String(data: authorizationCodeData, encoding: .utf8) else {
            completion(.failure(AuthError.appleSignInFailed))
            return
        }
        
        let user: AppleSignInRequest.AppleUser?
        if let fullName = appleIDCredential.fullName {
            user = AppleSignInRequest.AppleUser(
                name: AppleSignInRequest.AppleUser.PersonName(
                    firstName: fullName.givenName,
                    lastName: fullName.familyName
                ),
                email: appleIDCredential.email
            )
        } else {
            user = nil
        }
        
        let credential = AppleIDCredential(
            identityToken: identityToken,
            authorizationCode: authorizationCode,
            user: user
        )
        
        completion(.success(credential))
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        completion(.failure(error))
    }
    
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return UIWindow()
        }
        return window
    }
}

// MARK: - Supporting Types
// Note: ChangePasswordRequest is defined in User.swift to avoid duplication

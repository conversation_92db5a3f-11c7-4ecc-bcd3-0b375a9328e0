//
//  KeychainManager.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation
import Security
import Security

/// Keychain 管理器
class KeychainManager {
    static let shared = KeychainManager()
    
    // MARK: - Private Properties
    
    private let service = "com.taskflow.app"
    private let accessGroup: String? = nil // 如果需要应用间共享，可以设置 App Group
    
    // MARK: - Initialization
    
    private init() {}
    
    // MARK: - Public Methods
    
    /// 保存数据到 Keychain
    @discardableResult
    func set(_ value: String, forKey key: String) -> Bool {
        guard let data = value.data(using: .utf8) else { return false }
        return set(data, forKey: key)
    }
    
    /// 保存数据到 Keychain
    @discardableResult
    func set(_ data: Data, forKey key: String) -> Bool {
        // 先删除已存在的项目
        delete(key)
        
        var query = buildBaseQuery(forKey: key)
        query[kSecValueData as String] = data
        query[kSecAttrAccessible as String] = kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        
        let status = SecItemAdd(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    /// 从 Keychain 获取字符串
    func get(_ key: String) -> String? {
        guard let data = getData(key) else { return nil }
        return String(data: data, encoding: .utf8)
    }
    
    /// 从 Keychain 获取数据
    func getData(_ key: String) -> Data? {
        var query = buildBaseQuery(forKey: key)
        query[kSecReturnData as String] = true
        query[kSecMatchLimit as String] = kSecMatchLimitOne
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess else { return nil }
        return result as? Data
    }
    
    /// 从 Keychain 删除数据
    @discardableResult
    func delete(_ key: String) -> Bool {
        let query = buildBaseQuery(forKey: key)
        let status = SecItemDelete(query as CFDictionary)
        return status == errSecSuccess || status == errSecItemNotFound
    }
    
    /// 检查 Keychain 中是否存在指定的键
    func contains(_ key: String) -> Bool {
        var query = buildBaseQuery(forKey: key)
        query[kSecReturnData as String] = false
        query[kSecMatchLimit as String] = kSecMatchLimitOne
        
        let status = SecItemCopyMatching(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    /// 清除所有数据
    @discardableResult
    func clearAll() -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        return status == errSecSuccess || status == errSecItemNotFound
    }
    
    /// 获取所有键
    func getAllKeys() -> [String] {
        var query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecReturnAttributes as String: true,
            kSecMatchLimit as String: kSecMatchLimitAll
        ]
        
        if let accessGroup = accessGroup {
            query[kSecAttrAccessGroup as String] = accessGroup
        }
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess,
              let items = result as? [[String: Any]] else {
            return []
        }
        
        return items.compactMap { item in
            item[kSecAttrAccount as String] as? String
        }
    }
    
    // MARK: - Private Methods
    
    private func buildBaseQuery(forKey key: String) -> [String: Any] {
        var query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key
        ]
        
        if let accessGroup = accessGroup {
            query[kSecAttrAccessGroup as String] = accessGroup
        }
        
        return query
    }
}

// MARK: - Keychain Manager Extensions

extension KeychainManager {
    /// 保存用户认证信息
    func saveAuthCredentials(token: String, userId: String, email: String) -> Bool {
        let success1 = set(token, forKey: "auth_token")
        let success2 = set(userId, forKey: "user_id")
        let success3 = set(email, forKey: "user_email")
        
        return success1 && success2 && success3
    }
    
    /// 获取用户认证信息
    func getAuthCredentials() -> (token: String?, userId: String?, email: String?) {
        let token = get("auth_token")
        let userId = get("user_id")
        let email = get("user_email")
        
        return (token, userId, email)
    }
    
    /// 清除用户认证信息
    func clearAuthCredentials() -> Bool {
        let success1 = delete("auth_token")
        let success2 = delete("user_id")
        let success3 = delete("user_email")
        
        return success1 && success2 && success3
    }
    
    /// 检查是否有有效的认证信息
    func hasValidAuthCredentials() -> Bool {
        return contains("auth_token") && contains("user_id")
    }
}

// MARK: - Secure Storage for Sensitive Data

extension KeychainManager {
    /// 保存生物识别设置
    func setBiometricEnabled(_ enabled: Bool) -> Bool {
        return set(enabled ? "true" : "false", forKey: "biometric_enabled")
    }
    
    /// 获取生物识别设置
    func isBiometricEnabled() -> Bool {
        return get("biometric_enabled") == "true"
    }
    
    /// 保存应用锁定设置
    func setAppLockEnabled(_ enabled: Bool) -> Bool {
        return set(enabled ? "true" : "false", forKey: "app_lock_enabled")
    }
    
    /// 获取应用锁定设置
    func isAppLockEnabled() -> Bool {
        return get("app_lock_enabled") == "true"
    }
    
    /// 保存最后活跃时间
    func setLastActiveTime(_ time: Date) -> Bool {
        let timeString = ISO8601DateFormatter().string(from: time)
        return set(timeString, forKey: "last_active_time")
    }
    
    /// 获取最后活跃时间
    func getLastActiveTime() -> Date? {
        guard let timeString = get("last_active_time") else { return nil }
        return ISO8601DateFormatter().date(from: timeString)
    }
}

// MARK: - Error Handling

extension KeychainManager {
    enum KeychainError: LocalizedError {
        case itemNotFound
        case duplicateItem
        case invalidData
        case unexpectedError(OSStatus)
        
        var errorDescription: String? {
            switch self {
            case .itemNotFound:
                return "Keychain item not found"
            case .duplicateItem:
                return "Duplicate keychain item"
            case .invalidData:
                return "Invalid keychain data"
            case .unexpectedError(let status):
                return "Unexpected keychain error: \(status)"
            }
        }
    }
    
    /// 获取 Keychain 操作状态的错误描述
    func errorDescription(for status: OSStatus) -> String {
        switch status {
        case errSecSuccess:
            return "Success"
        case errSecItemNotFound:
            return "Item not found"
        case errSecDuplicateItem:
            return "Duplicate item"
        case errSecAuthFailed:
            return "Authentication failed"
        case -128: // errSecUserCancel
            return "User cancelled"
        case errSecNotAvailable:
            return "Not available"
        case errSecParam:
            return "Invalid parameter"
        case errSecAllocate:
            return "Memory allocation failed"
        case errSecUnimplemented:
            return "Function not implemented"
        case errSecDiskFull:
            return "Disk full"
        case errSecIO:
            return "I/O error"
        default:
            return "Unknown error: \(status)"
        }
    }
}

// MARK: - Debugging Support

#if DEBUG
extension KeychainManager {
    /// 打印所有 Keychain 项目（仅用于调试）
    func debugPrintAllItems() {
        let keys = getAllKeys()
        print("=== Keychain Items ===")
        for key in keys {
            let value = get(key) ?? "Unable to retrieve"
            print("\(key): \(value)")
        }
        print("======================")
    }
    
    /// 验证 Keychain 功能
    func debugTestKeychain() -> Bool {
        let testKey = "test_key"
        let testValue = "test_value"
        
        // 测试保存
        guard set(testValue, forKey: testKey) else {
            print("Failed to save test item")
            return false
        }
        
        // 测试读取
        guard let retrievedValue = get(testKey), retrievedValue == testValue else {
            print("Failed to retrieve test item")
            return false
        }
        
        // 测试删除
        guard delete(testKey) else {
            print("Failed to delete test item")
            return false
        }
        
        // 验证删除
        guard !contains(testKey) else {
            print("Test item still exists after deletion")
            return false
        }
        
        print("Keychain test passed")
        return true
    }
}
#endif

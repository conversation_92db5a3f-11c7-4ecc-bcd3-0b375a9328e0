//
//  ConfigurationManager.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation

/// 配置管理器
class ConfigurationManager {
    static let shared = ConfigurationManager()
    
    private var configuration: [String: Any] = [:]
    
    private init() {
        loadConfiguration()
    }
    
    // MARK: - Configuration Loading
    
    private func loadConfiguration() {
        guard let path = Bundle.main.path(forResource: "Configuration", ofType: "plist"),
              let plist = NSDictionary(contentsOfFile: path) as? [String: Any] else {
            print("⚠️ Warning: Could not load Configuration.plist, using default values")
            return
        }
        
        configuration = plist
    }
    
    // MARK: - Server Configuration
    
    var currentEnvironment: String {
        #if DEBUG
        return "Debug"
        #elseif DEVELOPMENT
        return "Development"
        #else
        return "Production"
        #endif
    }
    
    var baseURL: String {
        return getServerConfig(for: "BaseURL") ?? NetworkConfiguration.ServerConfig.current.baseURL
    }
    
    var parseServerURL: String {
        return getServerConfig(for: "ParseServerURL") ?? NetworkConfiguration.ServerConfig.current.parseServerURL
    }
    
    var allowInsecureHTTP: Bool {
        return getServerConfigBool(for: "AllowInsecureHTTP") ?? false
    }

    var enableLogging: Bool {
        return getServerConfigBool(for: "EnableLogging") ?? false
    }
    
    // MARK: - API Configuration
    
    var apiVersion: String {
        return getAPIConfig(for: "Version") as? String ?? "v1"
    }
    
    var timeout: TimeInterval {
        return TimeInterval(getAPIConfig(for: "Timeout") as? Int ?? 30)
    }
    
    var maxRetries: Int {
        return getAPIConfig(for: "MaxRetries") as? Int ?? 3
    }
    
    var userAgent: String {
        return getAPIConfig(for: "UserAgent") as? String ?? "TaskFlow-iOS/1.0"
    }
    
    // MARK: - Helper Methods
    
    private func getServerConfig(for key: String) -> String? {
        guard let serverConfig = configuration["ServerConfiguration"] as? [String: Any],
              let environmentConfig = serverConfig[currentEnvironment] as? [String: Any] else {
            return nil
        }
        return environmentConfig[key] as? String
    }

    private func getServerConfigBool(for key: String) -> Bool? {
        guard let serverConfig = configuration["ServerConfiguration"] as? [String: Any],
              let environmentConfig = serverConfig[currentEnvironment] as? [String: Any] else {
            return nil
        }
        return environmentConfig[key] as? Bool
    }
    
    private func getAPIConfig(for key: String) -> Any? {
        guard let apiConfig = configuration["APIConfiguration"] as? [String: Any] else {
            return nil
        }
        return apiConfig[key]
    }
    
    // MARK: - Debug Information
    
    func printCurrentConfiguration() {
        print("=== TaskFlow Configuration Manager ===")
        print("Environment: \(currentEnvironment)")
        print("Base URL: \(baseURL)")
        print("Parse Server URL: \(parseServerURL)")
        print("Allow Insecure HTTP: \(allowInsecureHTTP)")
        print("Enable Logging: \(enableLogging)")
        print("API Version: \(apiVersion)")
        print("Timeout: \(timeout)s")
        print("Max Retries: \(maxRetries)")
        print("User Agent: \(userAgent)")
        print("=====================================")
    }
}

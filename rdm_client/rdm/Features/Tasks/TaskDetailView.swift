//
//  TaskDetailView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct TaskDetailView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var taskService = TaskService.shared
    
    @State private var task: TaskModel
    @State private var showingEditView = false
    @State private var showingDeleteAlert = false
    @State private var isLoading = false
    
    init(task: TaskModel) {
        self._task = State(initialValue: task)
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Status and Priority
                    statusPrioritySection
                    
                    // Description
                    if let description = task.description, !description.isEmpty {
                        descriptionSection(description)
                    }
                    
                    // Metadata
                    metadataSection
                    
                    // Time Information
                    timeInformationSection
                    
                    // Tags
                    if !task.tags.isEmpty {
                        tagsSection
                    }
                    
                    // Actions
                    actionsSection
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .navigationTitle("任务详情")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("编辑") {
                        showingEditView = true
                    }
                }
            }
        }
        .sheet(isPresented: $showingEditView) {
            EditTaskView(task: task) { updatedTask in
                task = updatedTask
            }
        }
        .alert("删除任务", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) {}
            Button("删除", role: .destructive) {
                deleteTask()
            }
        } message: {
            Text("确定要删除这个任务吗？此操作无法撤销。")
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                // Completion Button
                Button(action: toggleCompletion) {
                    ZStack {
                        Circle()
                            .stroke(Color(task.priority.color).opacity(0.3), lineWidth: 3)
                            .frame(width: 32, height: 32)
                        
                        if task.isCompleted {
                            Image(systemName: "checkmark")
                                .font(.system(size: 16, weight: .bold))
                                .foregroundColor(Color.fromColorName(task.priority.color))
                        } else if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .progressViewStyle(CircularProgressViewStyle(tint: Color.fromColorName(task.priority.color)))
                        }
                    }
                }
                .disabled(isLoading)
                
                Spacer()
                
                // Priority Badge
                HStack(spacing: 4) {
                    Image(systemName: task.priority.iconName)
                        .font(.caption)
                    Text(task.priority.displayName)
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.fromColorName(task.priority.color).opacity(0.1))
                .foregroundColor(Color.fromColorName(task.priority.color))
                .cornerRadius(8)
            }
            
            // Title
            Text(task.title)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(task.isCompleted ? .secondary : .primary)
                .strikethrough(task.isCompleted)
        }
    }
    
    // MARK: - Status and Priority Section
    
    private var statusPrioritySection: some View {
        HStack(spacing: 16) {
            // Status
            VStack(alignment: .leading, spacing: 4) {
                Text("状态")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                HStack(spacing: 6) {
                    Image(systemName: task.status.iconName)
                        .font(.subheadline)
                        .foregroundColor(Color.fromColorName(task.status.color))

                    Text(task.status.displayName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(Color.fromColorName(task.status.color))
                }
            }
            
            Spacer()
            
            // Created Date
            VStack(alignment: .trailing, spacing: 4) {
                Text("创建时间")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(DateFormatter.mediumDate.string(from: task.createdAt))
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Description Section
    
    private func descriptionSection(_ description: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("描述")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(description)
                .font(.body)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
    
    // MARK: - Metadata Section
    
    private var metadataSection: some View {
        VStack(spacing: 12) {
            if let category = task.category, !category.isEmpty {
                InfoRow(
                    icon: "folder",
                    title: "分类",
                    value: category,
                    color: .blue
                )
            }
            
            if task.isOverdue && !task.isCompleted {
                InfoRow(
                    icon: "exclamationmark.triangle",
                    title: "状态",
                    value: task.overdueDescription ?? "已过期",
                    color: .red
                )
            }
        }
    }
    
    // MARK: - Time Information Section
    
    private var timeInformationSection: some View {
        VStack(spacing: 12) {
            if let dueDate = task.dueDate {
                InfoRow(
                    icon: "calendar",
                    title: "截止时间",
                    value: DateFormatter.fullDateTime.string(from: dueDate),
                    color: task.isOverdue ? .red : .orange
                )
            }
            
            if let estimatedDuration = task.estimatedDurationDescription {
                InfoRow(
                    icon: "clock",
                    title: "预估时长",
                    value: estimatedDuration,
                    color: .blue
                )
            }
            
            if let completedAt = task.completedAt {
                InfoRow(
                    icon: "checkmark.circle",
                    title: "完成时间",
                    value: DateFormatter.fullDateTime.string(from: completedAt),
                    color: .green
                )
            }
            
            if let actualDuration = task.completionDurationDescription {
                InfoRow(
                    icon: "timer",
                    title: "实际用时",
                    value: actualDuration,
                    color: .green
                )
            }
        }
    }
    
    // MARK: - Tags Section
    
    private var tagsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("标签")
                .font(.headline)
                .fontWeight(.semibold)
            
            FlowLayout(spacing: 8) {
                ForEach(task.tags, id: \.self) { tag in
                    Text("#\(tag)")
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.accentColor.opacity(0.1))
                        .foregroundColor(.accentColor)
                        .cornerRadius(8)
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Actions Section
    
    private var actionsSection: some View {
        VStack(spacing: 12) {
            if !task.isCompleted {
                Button(action: toggleCompletion) {
                    HStack {
                        Image(systemName: "checkmark.circle")
                        Text("标记为已完成")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.green)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                .disabled(isLoading)
            }
            
            Button(action: { showingDeleteAlert = true }) {
                HStack {
                    Image(systemName: "trash")
                    Text("删除任务")
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color.red)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
        }
    }
    
    // MARK: - Actions
    
    private func toggleCompletion() {
        isLoading = true

        Task {
            do {
                let updatedTask = try await taskService.toggleTaskCompletion(task)
                await MainActor.run {
                    task = updatedTask
                    isLoading = false
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                }
            }
        }
    }
    
    private func deleteTask() {
        Task {
            do {
                try await taskService.deleteTask(task)
                await MainActor.run {
                    dismiss()
                }
            } catch {
                // Handle error
            }
        }
    }
}

// MARK: - Info Row

struct InfoRow: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(color)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(value)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

// MARK: - Flow Layout

struct FlowLayout: Layout {
    let spacing: CGFloat
    
    init(spacing: CGFloat = 8) {
        self.spacing = spacing
    }
    
    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        let result = FlowResult(
            in: proposal.replacingUnspecifiedDimensions().width,
            subviews: subviews,
            spacing: spacing
        )
        return result.size
    }
    
    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let result = FlowResult(
            in: bounds.width,
            subviews: subviews,
            spacing: spacing
        )
        
        for (index, subview) in subviews.enumerated() {
            subview.place(at: result.positions[index], proposal: .unspecified)
        }
    }
}

struct FlowResult {
    let size: CGSize
    let positions: [CGPoint]
    
    init(in maxWidth: CGFloat, subviews: LayoutSubviews, spacing: CGFloat) {
        var positions: [CGPoint] = []
        var currentPosition = CGPoint.zero
        var lineHeight: CGFloat = 0
        var maxY: CGFloat = 0
        
        for subview in subviews {
            let subviewSize = subview.sizeThatFits(.unspecified)
            
            if currentPosition.x + subviewSize.width > maxWidth && currentPosition.x > 0 {
                currentPosition.x = 0
                currentPosition.y += lineHeight + spacing
                lineHeight = 0
            }
            
            positions.append(currentPosition)
            currentPosition.x += subviewSize.width + spacing
            lineHeight = max(lineHeight, subviewSize.height)
            maxY = max(maxY, currentPosition.y + subviewSize.height)
        }
        
        self.positions = positions
        self.size = CGSize(width: maxWidth, height: maxY)
    }
}

// MARK: - Date Formatters

extension DateFormatter {
    static let mediumDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }()
    
    static let fullDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter
    }()
}

#Preview {
    TaskDetailView(task: TaskModel.preview())
}

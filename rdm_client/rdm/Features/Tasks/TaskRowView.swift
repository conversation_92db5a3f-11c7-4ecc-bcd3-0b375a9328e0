//
//  TaskRowView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct TaskRowView: View {
    let task: TaskModel
    let onTap: () -> Void
    let onToggleComplete: () -> Void
    
    @State private var isCompleting = false
    
    var body: some View {
        HStack(spacing: 12) {
            // Completion Button
            completionButton
            
            // Task Content
            VStack(alignment: .leading, spacing: 4) {
                // Title and Priority
                titleSection
                
                // Description
                if let description = task.description, !description.isEmpty {
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                // Metadata
                metadataSection
            }
            
            Spacer()
            
            // Due Date and Status
            rightSection
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(task.isCompleted ? Color(.systemGray6) : Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(borderColor, lineWidth: 1)
        )
        .opacity(task.isCompleted ? 0.7 : 1.0)
        .onTapGesture {
            onTap()
        }
    }
    
    // MARK: - Completion Button
    
    private var completionButton: some View {
        Button(action: toggleCompletion) {
            ZStack {
                Circle()
                    .stroke(Color(task.priority.color).opacity(0.3), lineWidth: 2)
                    .frame(width: 24, height: 24)
                
                if task.isCompleted {
                    Image(systemName: "checkmark")
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(Color(task.priority.color))
                } else if isCompleting {
                    ProgressView()
                        .scaleEffect(0.6)
                        .progressViewStyle(CircularProgressViewStyle(tint: Color(task.priority.color)))
                }
            }
        }
        .disabled(isCompleting)
    }
    
    // MARK: - Title Section
    
    private var titleSection: some View {
        HStack(spacing: 8) {
            Text(task.title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(task.isCompleted ? .secondary : .primary)
                .strikethrough(task.isCompleted)
            
            // Priority Indicator
            if task.priority != .low {
                Image(systemName: task.priority.iconName)
                    .font(.caption)
                    .foregroundColor(Color.fromColorName(task.priority.color))
            }
        }
    }
    
    // MARK: - Metadata Section
    
    private var metadataSection: some View {
        HStack(spacing: 12) {
            // Category
            if let category = task.category, !category.isEmpty {
                Label(category, systemImage: "folder")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            // Tags
            if !task.tags.isEmpty {
                HStack(spacing: 4) {
                    ForEach(task.tags.prefix(2), id: \.self) { tag in
                        Text("#\(tag)")
                            .font(.caption2)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.accentColor.opacity(0.1))
                            .foregroundColor(.accentColor)
                            .cornerRadius(4)
                    }
                    
                    if task.tags.count > 2 {
                        Text("+\(task.tags.count - 2)")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
        }
    }
    
    // MARK: - Right Section
    
    private var rightSection: some View {
        VStack(alignment: .trailing, spacing: 4) {
            // Due Date or Status
            if let dueDate = task.dueDate {
                dueDateView(dueDate)
            } else {
                statusView
            }
            
            // Estimated Duration
            if let estimatedDuration = task.estimatedDurationDescription {
                Label(estimatedDuration, systemImage: "clock")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - Due Date View
    
    private func dueDateView(_ dueDate: Date) -> some View {
        VStack(alignment: .trailing, spacing: 2) {
            if task.isOverdue {
                Text(task.overdueDescription ?? "已过期")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.red)
            } else if let timeRemaining = task.timeRemainingDescription {
                Text(timeRemaining)
                    .font(.caption)
                    .foregroundColor(.orange)
            } else {
                Text(DateFormatter.shortDate.string(from: dueDate))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Text(DateFormatter.time.string(from: dueDate))
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Status View
    
    private var statusView: some View {
        HStack(spacing: 4) {
            Image(systemName: task.status.iconName)
                .font(.caption)
                .foregroundColor(Color.fromColorName(task.status.color))

            Text(task.status.displayName)
                .font(.caption)
                .foregroundColor(Color.fromColorName(task.status.color))
        }
    }
    
    // MARK: - Computed Properties
    
    private var borderColor: Color {
        if task.isOverdue && !task.isCompleted {
            return .red.opacity(0.3)
        } else if task.priority == .high && !task.isCompleted {
            return Color.fromColorName(task.priority.color).opacity(0.3)
        } else {
            return .clear
        }
    }
    
    // MARK: - Actions
    
    private func toggleCompletion() {
        isCompleting = true
        
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // 延迟执行以显示动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            onToggleComplete()
            isCompleting = false
        }
    }
}

// MARK: - Date Formatters

extension DateFormatter {
    static let shortDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .none
        return formatter
    }()
    
    static let time: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return formatter
    }()
}



#Preview {
    VStack(spacing: 16) {
        TaskRowView(
            task: TaskModel.preview(),
            onTap: {},
            onToggleComplete: {}
        )
        
        TaskRowView(
            task: TaskModel.completedPreview(),
            onTap: {},
            onToggleComplete: {}
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}

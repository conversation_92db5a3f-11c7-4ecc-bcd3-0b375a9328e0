//
//  HabitFilterView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct HabitFilterView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var habitService = HabitService.shared
    
    @Binding var selectedFilter: HabitFilter
    
    @State private var selectedFrequency: Habit.Frequency?
    @State private var selectedType: Habit.HabitType?
    @State private var isActiveFilter: Bool?
    @State private var sortBy: HabitSortOption = .createdAt
    @State private var sortOrder: SortOrder = .descending
    
    var body: some View {
        NavigationView {
            Form {
                // Quick Filters
                quickFiltersSection
                
                // Frequency Filter
                frequencyFilterSection
                
                // Type Filter
                typeFilterSection
                
                // Status Filter
                statusFilterSection
                
                // Sort Options
                sortOptionsSection
            }
            .navigationTitle("筛选和排序")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("重置") {
                        resetFilters()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        applyFilters()
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - Quick Filters Section
    
    private var quickFiltersSection: some View {
        Section("快速筛选") {
            ForEach(HabitFilter.allCases, id: \.self) { filter in
                HStack {
                    Button(action: {
                        selectedFilter = filter
                        setQuickFilter(filter)
                    }) {
                        HStack {
                            Text(filter.displayName)
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            Text("\(getFilterCount(filter))")
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(Color.accentColor.opacity(0.2))
                                .foregroundColor(.accentColor)
                                .cornerRadius(8)
                        }
                    }
                    
                    if selectedFilter == filter {
                        Image(systemName: "checkmark")
                            .foregroundColor(.accentColor)
                    }
                }
            }
        }
    }
    
    // MARK: - Frequency Filter Section
    
    private var frequencyFilterSection: some View {
        Section("频率") {
            Picker("频率", selection: $selectedFrequency) {
                Text("全部").tag(nil as Habit.Frequency?)
                
                ForEach(Habit.Frequency.allCases, id: \.self) { frequency in
                    HStack {
                        Image(systemName: frequency.iconName)
                        Text(frequency.displayName)
                    }
                    .tag(frequency as Habit.Frequency?)
                }
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
    
    // MARK: - Type Filter Section
    
    private var typeFilterSection: some View {
        Section("类型") {
            Picker("类型", selection: $selectedType) {
                Text("全部").tag(nil as Habit.HabitType?)
                
                ForEach(Habit.HabitType.allCases, id: \.self) { type in
                    HStack {
                        Image(systemName: type.iconName)
                        Text(type.displayName)
                    }
                    .tag(type as Habit.HabitType?)
                }
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
    
    // MARK: - Status Filter Section
    
    private var statusFilterSection: some View {
        Section("状态") {
            Picker("状态", selection: $isActiveFilter) {
                Text("全部").tag(nil as Bool?)
                Text("活跃").tag(true as Bool?)
                Text("暂停").tag(false as Bool?)
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
    
    // MARK: - Sort Options Section
    
    private var sortOptionsSection: some View {
        Section("排序") {
            Picker("排序方式", selection: $sortBy) {
                ForEach(HabitSortOption.allCases, id: \.self) { option in
                    Text(option.displayName).tag(option)
                }
            }
            .pickerStyle(MenuPickerStyle())
            
            Picker("排序顺序", selection: $sortOrder) {
                ForEach(SortOrder.allCases, id: \.self) { order in
                    Text(order.displayName).tag(order)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
    
    // MARK: - Helper Methods
    
    private func getFilterCount(_ filter: HabitFilter) -> Int {
        switch filter {
        case .all:
            return habitService.habits.count
        case .active:
            return habitService.activeHabits.count
        case .today:
            return habitService.todayHabits.count
        case .completed:
            return habitService.todayHabits.filter { habitService.isHabitCompletedToday($0.id) }.count
        case .pending:
            return habitService.todayHabits.filter { !habitService.isHabitCompletedToday($0.id) }.count
        }
    }
    
    private func setQuickFilter(_ filter: HabitFilter) {
        resetFilters()
        
        switch filter {
        case .all:
            break
        case .active:
            isActiveFilter = true
        case .today, .completed, .pending:
            break // These are handled by computed properties
        }
    }
    
    private func resetFilters() {
        selectedFrequency = nil
        selectedType = nil
        isActiveFilter = nil
        sortBy = .createdAt
        sortOrder = .descending
    }
    
    private func applyFilters() {
        Task {
            await habitService.fetchHabits(
                frequency: selectedFrequency,
                type: selectedType,
                isActive: isActiveFilter,
                sortBy: sortBy.rawValue,
                sortOrder: sortOrder.rawValue
            )
        }
    }
}

// MARK: - Supporting Types

enum HabitSortOption: String, CaseIterable {
    case createdAt = "createdAt"
    case name = "name"
    case frequency = "frequency"
    case currentStreak = "currentStreak"
    case bestStreak = "bestStreak"
    
    var displayName: String {
        switch self {
        case .createdAt:
            return "创建时间"
        case .name:
            return "名称"
        case .frequency:
            return "频率"
        case .currentStreak:
            return "当前连续"
        case .bestStreak:
            return "最佳记录"
        }
    }
}

enum SortOrder: String, CaseIterable {
    case ascending = "asc"
    case descending = "desc"

    var displayName: String {
        switch self {
        case .ascending:
            return "升序"
        case .descending:
            return "降序"
        }
    }
}

#Preview {
    HabitFilterView(selectedFilter: .constant(.all))
}

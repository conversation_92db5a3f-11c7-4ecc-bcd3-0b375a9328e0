//
//  ChangePasswordView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct ChangePasswordView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var authService = AuthService.shared
    
    @State private var currentPassword = ""
    @State private var newPassword = ""
    @State private var confirmPassword = ""
    @State private var isCurrentPasswordVisible = false
    @State private var isNewPasswordVisible = false
    @State private var isConfirmPasswordVisible = false
    
    @State private var isLoading = false
    @State private var showingSuccess = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                // Instructions
                instructionsSection
                
                // Password Fields
                passwordFieldsSection
                
                // Password Requirements
                passwordRequirementsSection
            }
            .navigationTitle("修改密码")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        changePassword()
                    }
                    .disabled(!isFormValid || isLoading)
                }
            }
        }
        .alert("密码修改成功", isPresented: $showingSuccess) {
            Button("确定") {
                dismiss()
            }
        } message: {
            Text("您的密码已成功修改。")
        }
        .alert("错误", isPresented: $showingError) {
            Button("确定") {}
        } message: {
            Text(errorMessage)
        }
    }
    
    // MARK: - Instructions Section
    
    private var instructionsSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: "info.circle")
                        .foregroundColor(.blue)
                    Text("密码修改说明")
                        .font(.headline)
                        .fontWeight(.medium)
                }
                
                Text("为了保护您的账户安全，请输入当前密码以验证身份，然后设置新密码。")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 4)
        }
    }
    
    // MARK: - Password Fields Section
    
    private var passwordFieldsSection: some View {
        Section("密码信息") {
            // Current Password
            VStack(alignment: .leading, spacing: 8) {
                Text("当前密码")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack {
                    Image(systemName: "lock")
                        .foregroundColor(.secondary)
                        .frame(width: 20)
                    
                    if isCurrentPasswordVisible {
                        TextField("请输入当前密码", text: $currentPassword)
                            .textFieldStyle(PlainTextFieldStyle())
                    } else {
                        SecureField("请输入当前密码", text: $currentPassword)
                            .textFieldStyle(PlainTextFieldStyle())
                    }
                    
                    Button(action: { isCurrentPasswordVisible.toggle() }) {
                        Image(systemName: isCurrentPasswordVisible ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 10)
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            
            // New Password
            VStack(alignment: .leading, spacing: 8) {
                Text("新密码")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack {
                    Image(systemName: "lock.fill")
                        .foregroundColor(.secondary)
                        .frame(width: 20)
                    
                    if isNewPasswordVisible {
                        TextField("请输入新密码", text: $newPassword)
                            .textFieldStyle(PlainTextFieldStyle())
                    } else {
                        SecureField("请输入新密码", text: $newPassword)
                            .textFieldStyle(PlainTextFieldStyle())
                    }
                    
                    Button(action: { isNewPasswordVisible.toggle() }) {
                        Image(systemName: isNewPasswordVisible ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 10)
                .background(Color(.systemGray6))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(passwordStrengthColor, lineWidth: 1)
                )
            }
            
            // Confirm Password
            VStack(alignment: .leading, spacing: 8) {
                Text("确认新密码")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack {
                    Image(systemName: "lock.fill")
                        .foregroundColor(.secondary)
                        .frame(width: 20)
                    
                    if isConfirmPasswordVisible {
                        TextField("请再次输入新密码", text: $confirmPassword)
                            .textFieldStyle(PlainTextFieldStyle())
                    } else {
                        SecureField("请再次输入新密码", text: $confirmPassword)
                            .textFieldStyle(PlainTextFieldStyle())
                    }
                    
                    Button(action: { isConfirmPasswordVisible.toggle() }) {
                        Image(systemName: isConfirmPasswordVisible ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 10)
                .background(Color(.systemGray6))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(passwordsMatch ? .green : .clear, lineWidth: 1)
                )
            }
        }
    }
    
    // MARK: - Password Requirements Section
    
    private var passwordRequirementsSection: some View {
        Section("密码要求") {
            VStack(alignment: .leading, spacing: 8) {
                PasswordRequirement(
                    text: "至少8个字符",
                    isMet: newPassword.count >= 8
                )
                
                PasswordRequirement(
                    text: "包含大写字母",
                    isMet: newPassword.range(of: "[A-Z]", options: .regularExpression) != nil
                )
                
                PasswordRequirement(
                    text: "包含小写字母",
                    isMet: newPassword.range(of: "[a-z]", options: .regularExpression) != nil
                )
                
                PasswordRequirement(
                    text: "包含数字",
                    isMet: newPassword.range(of: "[0-9]", options: .regularExpression) != nil
                )
                
                PasswordRequirement(
                    text: "两次输入的密码一致",
                    isMet: passwordsMatch && !confirmPassword.isEmpty
                )
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        !currentPassword.isEmpty &&
        isNewPasswordValid &&
        passwordsMatch
    }
    
    private var isNewPasswordValid: Bool {
        newPassword.count >= 8 &&
        newPassword.range(of: "[A-Z]", options: .regularExpression) != nil &&
        newPassword.range(of: "[a-z]", options: .regularExpression) != nil &&
        newPassword.range(of: "[0-9]", options: .regularExpression) != nil
    }
    
    private var passwordsMatch: Bool {
        !newPassword.isEmpty && newPassword == confirmPassword
    }
    
    private var passwordStrengthColor: Color {
        if newPassword.isEmpty {
            return .clear
        } else if isNewPasswordValid {
            return .green
        } else if newPassword.count >= 6 {
            return .orange
        } else {
            return .red
        }
    }
    
    // MARK: - Actions
    
    private func changePassword() {
        isLoading = true
        errorMessage = ""

        Task {
            do {
                try await authService.changePassword(
                    currentPassword: currentPassword,
                    newPassword: newPassword
                )

                await MainActor.run {
                    isLoading = false
                    showingSuccess = true
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = error.localizedDescription
                    showingError = true
                }
            }
        }
    }
}

// MARK: - Password Requirement

struct PasswordRequirement: View {
    let text: String
    let isMet: Bool
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: isMet ? "checkmark.circle.fill" : "circle")
                .foregroundColor(isMet ? .green : .secondary)
                .font(.subheadline)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(isMet ? .primary : .secondary)
            
            Spacer()
        }
    }
}

#Preview {
    ChangePasswordView()
}
